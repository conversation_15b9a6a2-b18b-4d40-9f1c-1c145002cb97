// Generated by React Router

import "react-router"

declare module "react-router" {
  interface Register {
    pages: Pages
    routeFiles: RouteFiles
  }
}

type Pages = {
  "/": {
    params: {};
  };
  "/sign-in/*": {
    params: {
      "*": string;
    };
  };
  "/sign-up/*": {
    params: {
      "*": string;
    };
  };
  "/dashboard": {
    params: {};
  };
  "/dashboard/grants": {
    params: {};
  };
  "/dashboard/applications": {
    params: {};
  };
  "/dashboard/deadlines": {
    params: {};
  };
  "/dashboard/documents": {
    params: {};
  };
  "/dashboard/analytics": {
    params: {};
  };
  "/dashboard/chat": {
    params: {};
  };
  "/dashboard/settings": {
    params: {};
  };
};

type RouteFiles = {
  "root.tsx": {
    id: "root";
    page: "/" | "/sign-in/*" | "/sign-up/*" | "/dashboard" | "/dashboard/grants" | "/dashboard/applications" | "/dashboard/deadlines" | "/dashboard/documents" | "/dashboard/analytics" | "/dashboard/chat" | "/dashboard/settings";
  };
  "routes/home.tsx": {
    id: "routes/home";
    page: "/";
  };
  "routes/sign-in.tsx": {
    id: "routes/sign-in";
    page: "/sign-in/*";
  };
  "routes/sign-up.tsx": {
    id: "routes/sign-up";
    page: "/sign-up/*";
  };
  "routes/dashboard/layout.tsx": {
    id: "routes/dashboard/layout";
    page: "/dashboard" | "/dashboard/grants" | "/dashboard/applications" | "/dashboard/deadlines" | "/dashboard/documents" | "/dashboard/analytics" | "/dashboard/chat" | "/dashboard/settings";
  };
  "routes/dashboard/index.tsx": {
    id: "routes/dashboard/index";
    page: "/dashboard";
  };
  "routes/dashboard/grants.tsx": {
    id: "routes/dashboard/grants";
    page: "/dashboard/grants";
  };
  "routes/dashboard/applications.tsx": {
    id: "routes/dashboard/applications";
    page: "/dashboard/applications";
  };
  "routes/dashboard/deadlines.tsx": {
    id: "routes/dashboard/deadlines";
    page: "/dashboard/deadlines";
  };
  "routes/dashboard/documents.tsx": {
    id: "routes/dashboard/documents";
    page: "/dashboard/documents";
  };
  "routes/dashboard/analytics.tsx": {
    id: "routes/dashboard/analytics";
    page: "/dashboard/analytics";
  };
  "routes/dashboard/chat.tsx": {
    id: "routes/dashboard/chat";
    page: "/dashboard/chat";
  };
  "routes/dashboard/settings.tsx": {
    id: "routes/dashboard/settings";
    page: "/dashboard/settings";
  };
};
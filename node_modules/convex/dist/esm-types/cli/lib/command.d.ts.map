{"version": 3, "file": "command.d.ts", "sourceRoot": "", "sources": ["../../../../src/cli/lib/command.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,SAAS,EAAE,MAAM,0BAA0B,CAAC;AACrD,OAAO,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AASpC,OAAO,QAAQ,6BAA6B,CAAC;IAC3C,UAAU,OAAO,CAAC,IAAI,SAAS,GAAG,EAAE,GAAG,EAAE,EAAE,IAAI,SAAS,YAAY,GAAG,EAAE;QACvE;;;;;;;;;;;;;;WAcG;QACH,6BAA6B,CAAC,MAAM,EAAE,iBAAiB,GAAG,OAAO,CAC/D,IAAI,EACJ,IAAI,GAAG;YACL,OAAO,CAAC,EAAE,MAAM,CAAC;YACjB,GAAG,CAAC,EAAE,MAAM,CAAC;YACb,QAAQ,CAAC,EAAE,MAAM,CAAC;YAClB,IAAI,CAAC,EAAE,OAAO,CAAC;YACf,WAAW,CAAC,EAAE,MAAM,CAAC;YACrB,cAAc,CAAC,EAAE,MAAM,CAAC;SACzB,CACF,CAAC;QAEF;;WAEG;QACH,gBAAgB,IAAI,OAAO,CACzB,IAAI,EACJ,IAAI,GAAG;YACL,OAAO,CAAC,EAAE,OAAO,CAAC;YAClB,MAAM,CAAC,EAAE,OAAO,CAAC;YACjB,GAAG,CAAC,EAAE,OAAO,CAAC;YACd,SAAS,EAAE,QAAQ,GAAG,KAAK,GAAG,SAAS,CAAC;YACxC,mBAAmB,EAAE,OAAO,CAAC;YAC7B,OAAO,EAAE,QAAQ,GAAG,SAAS,CAAC;YAC9B,GAAG,CAAC,EAAE,MAAM,CAAC;YACb,gBAAgB,CAAC,EAAE,MAAM,CAAC;YAC1B,eAAe,CAAC,EAAE,MAAM,CAAC;YACzB,KAAK,CAAC,EAAE,OAAO,CAAC;YAChB,gBAAgB,CAAC,EAAE,MAAM,CAAC;YAC1B,oBAAoB,CAAC,EAAE,OAAO,CAAC;SAChC,CACF,CAAC;QAEF;;WAEG;QACH,kBAAkB,IAAI,OAAO,CAC3B,IAAI,EACJ,IAAI,GAAG;YACL,GAAG,CAAC,EAAE,MAAM,CAAC;YACb,QAAQ,CAAC,EAAE,MAAM,CAAC;YAClB,GAAG,CAAC,EAAE,MAAM,CAAC;SACd,CACF,CAAC;QAEF;;WAEG;QACH,aAAa,IAAI,OAAO,CACtB;YAAC,GAAG,IAAI;YAAE,MAAM;YAAE,MAAM,GAAG,SAAS;SAAC,EACrC,IAAI,GAAG;YACL,KAAK,CAAC,EAAE,OAAO,CAAC;YAChB,IAAI,CAAC,EAAE,OAAO,CAAC;YACf,QAAQ,CAAC,EAAE,MAAM,CAAC;YAClB,SAAS,EAAE,QAAQ,GAAG,KAAK,GAAG,SAAS,CAAC;YACxC,mBAAmB,EAAE,OAAO,CAAC;YAC7B,OAAO,EAAE,QAAQ,GAAG,SAAS,CAAC;YAC9B,SAAS,CAAC,EAAE,MAAM,CAAC;YACnB,oBAAoB,CAAC,EAAE,OAAO,CAAC;SAChC,CACF,CAAC;QAEF;;WAEG;QACH,gBAAgB,IAAI,OAAO,CACzB;YAAC,GAAG,IAAI;YAAE,MAAM;SAAC,EACjB,IAAI,GAAG;YACL,KAAK,CAAC,EAAE,MAAM,CAAC;YACf,MAAM,CAAC,EAAE,KAAK,GAAG,WAAW,GAAG,WAAW,GAAG,KAAK,CAAC;YACnD,OAAO,CAAC,EAAE,OAAO,CAAC;YAClB,MAAM,CAAC,EAAE,OAAO,CAAC;YACjB,UAAU,CAAC,EAAE,OAAO,CAAC;YACrB,GAAG,CAAC,EAAE,OAAO,CAAC;YACd,SAAS,CAAC,EAAE,MAAM,CAAC;SACpB,CACF,CAAC;QAEF;;WAEG;QACH,gBAAgB,IAAI,OAAO,CACzB,IAAI,EACJ,IAAI,GAAG;YACL,IAAI,EAAE,MAAM,CAAC;YACb,kBAAkB,CAAC,EAAE,OAAO,CAAC;SAC9B,CACF,CAAC;QAEF;;WAEG;QACH,cAAc,IAAI,OAAO,CACvB;YAAC,GAAG,IAAI;YAAE,MAAM,GAAG,SAAS;SAAC,EAC7B,IAAI,GAAG;YACL,KAAK,EAAE,MAAM,CAAC;YACd,KAAK,EAAE,KAAK,GAAG,MAAM,CAAC;YACtB,SAAS,CAAC,EAAE,MAAM,CAAC;SACpB,CACF,CAAC;QAEF;;WAEG;QACH,cAAc,IAAI,OAAO,CACvB,IAAI,EACJ,IAAI,GAAG;YACL,OAAO,EAAE,MAAM,CAAC;YAChB,OAAO,EAAE,OAAO,CAAC;SAClB,CACF,CAAC;QAEF;;WAEG;QACH,qBAAqB,IAAI,OAAO,CAC9B,IAAI,EACJ,IAAI,GAAG;YACL,OAAO,CAAC,EAAE,MAAM,CAAC;YACjB,QAAQ,CAAC,EAAE,MAAM,CAAC;YAClB,SAAS,CAAC,EAAE,OAAO,CAAC;SACrB,CACF,CAAC;KACH;CACF;AAuCD,OAAO,CAAC,MAAM,GAAG,EAAE,OAAO,MAAM,CAAC;AACjC,KAAK,iBAAiB,GAAG,MAAM,GAAG;IAAE,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAE,MAAM,CAAA;CAAE,CAAC;AAC7D,wBAAgB,iBAAiB,CAAC,MAAM,EAAE,MAAM,GAAG,iBAAiB,CAEnE;AAED,wBAAsB,mBAAmB,CACvC,GAAG,EAAE,SAAS,EACd,UAAU,EAAE;IACV,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB,SAAS,EAAE,QAAQ,GAAG,KAAK,GAAG,SAAS,CAAC;IACxC,mBAAmB,CAAC,EAAE,OAAO,CAAC;IAC9B,OAAO,EAAE,QAAQ,GAAG,SAAS,CAAC;IAC9B,IAAI,CAAC,EAAE,OAAO,CAAC;IACf,YAAY,EAAE,OAAO,CAAC;IACtB,GAAG,CAAC,EAAE,MAAM,CAAC;IACb,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,QAAQ,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IACzB,WAAW,EAAE,OAAO,CAAC;IACrB,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB,oBAAoB,CAAC,EAAE,OAAO,CAAC;IAC/B,KAAK,CAAC,EAAE,MAAM,CAAC;CAChB,GACA,OAAO,CAAC;IACT,OAAO,EAAE,OAAO,CAAC;IACjB,SAAS,EAAE,QAAQ,GAAG,KAAK,GAAG,SAAS,CAAC;IACxC,mBAAmB,EAAE,OAAO,CAAC;IAC7B,OAAO,EAAE,OAAO,CAAC;IACjB,IAAI,EAAE,OAAO,CAAC;IACd,YAAY,EAAE,OAAO,CAAC;IACtB,GAAG,CAAC,EACA;QAAE,IAAI,EAAE,UAAU,CAAC;QAAC,IAAI,EAAE,MAAM,CAAC;QAAC,SAAS,CAAC,EAAE,MAAM,CAAA;KAAE,GACtD;QAAE,IAAI,EAAE,OAAO,CAAC;QAAC,OAAO,EAAE,MAAM,CAAA;KAAE,CAAC;IACvC,QAAQ,EAAE,OAAO,CAAC;IAClB,WAAW,EAAE,OAAO,CAAC;IACrB,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB,oBAAoB,EAAE,OAAO,CAAC;CAC/B,CAAC,CA6CD"}
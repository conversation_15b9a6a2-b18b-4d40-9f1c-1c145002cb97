{"version": 3, "file": "configure.d.ts", "sourceRoot": "", "sources": ["../../../src/cli/configure.ts"], "names": [], "mappings": "AACA,OAAO,EACL,OAAO,EAMR,MAAM,uBAAuB,CAAC;AAC/B,OAAO,EACL,cAAc,EACd,cAAc,EAGd,gCAAgC,EAIjC,MAAM,cAAc,CAAC;AAkCtB,OAAO,EACL,mBAAmB,EAIpB,MAAM,8BAA8B,CAAC;AAGtC,KAAK,qBAAqB,GAAG;IAC3B,GAAG,EAAE,MAAM,CAAC;IACZ,QAAQ,EAAE,MAAM,CAAC;CAClB,CAAC;AAEF,KAAK,mBAAmB,GAEpB,KAAK,GAEL,UAAU,GAEV,KAAK,GAEL,IAAI,CAAC;AAET,KAAK,mBAAmB,GAAG;IACzB,sBAAsB,EAAE,gCAAgC,CAAC;IACzD,IAAI,EAAE,OAAO,CAAC;IACd,YAAY,EAAE;QACZ,KAAK,CAAC,EAAE;YACN,KAAK,EAAE,MAAM,CAAC;YACd,IAAI,EAAE,MAAM,CAAC;SACd,CAAC;QACF,cAAc,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;QACpC,gBAAgB,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;QACtC,YAAY,EAAE,OAAO,CAAC;KACvB,CAAC;IACF,IAAI,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAC1B,OAAO,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAC7B,aAAa,CAAC,EAAE,OAAO,GAAG,OAAO,GAAG,SAAS,CAAC;IAC9C,KAAK,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;IAC5B,KAAK,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;IAC5B,GAAG,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IACzB,QAAQ,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAC9B,OAAO,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAC7B,eAAe,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IACrC,kBAAkB,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IACxC,oBAAoB,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAC1C,oBAAoB,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;CAC3C,CAAC;AAEF;;;;;;GAMG;AACH,wBAAsB,gCAAgC,CACpD,GAAG,EAAE,OAAO,EACZ,mBAAmB,EAAE,mBAAmB,EACxC,mBAAmB,EAAE,mBAAmB,EACxC,UAAU,EAAE,mBAAmB,EAC/B,WAAW,CAAC,EAAE,MAAM,GAAG,SAAS,GAC/B,OAAO,CACR,qBAAqB,GAAG;IACtB,gBAAgB,EAAE;QAChB,cAAc,EAAE,cAAc,CAAC;QAC/B,cAAc,EAAE,MAAM,CAAC;QACvB,WAAW,EAAE,MAAM,GAAG,IAAI,CAAC;QAC3B,QAAQ,EAAE,MAAM,GAAG,IAAI,CAAC;KACzB,GAAG,IAAI,CAAC;CACV,CACF,CAkCA;AAED,wBAAsB,iCAAiC,CACrD,GAAG,EAAE,OAAO,EACZ,mBAAmB,EAAE,mBAAmB,EACxC,mBAAmB,EAAE,mBAAmB,EACxC,UAAU,EAAE,mBAAmB,EAC/B,WAAW,CAAC,EAAE,MAAM,GAAG,SAAS,GAC/B,OAAO,CACR,qBAAqB,GAAG;IACtB,gBAAgB,EAAE;QAChB,cAAc,EAAE,cAAc,CAAC;QAC/B,cAAc,EAAE,cAAc,CAAC;QAC/B,WAAW,EAAE,MAAM,GAAG,IAAI,CAAC;QAC3B,QAAQ,EAAE,MAAM,GAAG,IAAI,CAAC;KACzB,GAAG,IAAI,CAAC;CACV,CACF,CAsIA;AAuJD,wBAAsB,+BAA+B,CACnD,GAAG,EAAE,OAAO,EACZ,UAAU,EAAE;IAAE,GAAG,EAAE,MAAM,CAAC;IAAC,QAAQ,EAAE,MAAM,CAAA;CAAE;;;GAsB9C;AAED,wBAAsB,aAAa,CACjC,GAAG,EAAE,OAAO,EACZ,mBAAmB,EAAE,mBAAmB,EACxC,UAAU,EAAE;IACV,IAAI,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAC1B,OAAO,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAC7B,aAAa,CAAC,EAAE,OAAO,GAAG,OAAO,GAAG,SAAS,CAAC;IAC9C,KAAK,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;IAC5B,KAAK,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;IAC5B,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,kBAAkB,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;CACzC,GACA,OAAO,CAAC;IACT,QAAQ,EAAE,MAAM,CAAC;IACjB,WAAW,EAAE,MAAM,CAAC;IACpB,aAAa,EAAE,OAAO,GAAG,OAAO,CAAC;CAClC,CAAC,CAkBD;AAqOD,wBAAsB,wCAAwC,CAC5D,GAAG,EAAE,OAAO,EACZ,OAAO,EAAE;IACP,GAAG,EAAE,MAAM,CAAC;IACZ,cAAc,EAAE,MAAM,CAAC;IACvB,QAAQ,EAAE,MAAM,GAAG,IAAI,CAAC;IACxB,WAAW,EAAE,MAAM,GAAG,IAAI,CAAC;IAC3B,cAAc,EAAE,cAAc,CAAC;CAChC,EACD,aAAa,EAAE,MAAM,GAAG,IAAI,iBAkC7B"}
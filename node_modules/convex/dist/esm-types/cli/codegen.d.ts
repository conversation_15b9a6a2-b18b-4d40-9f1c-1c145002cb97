import { Command } from "@commander-js/extra-typings";
export declare const codegen: Command<[], {
    dryRun?: true | undefined;
    debug?: true | undefined;
    typecheck: "enable" | "try" | "disable";
    init?: true | undefined;
    adminKey?: string | undefined;
    url?: string | undefined;
    liveComponentSources?: true | undefined;
    commonjs?: true | undefined;
}>;
//# sourceMappingURL=codegen.d.ts.map
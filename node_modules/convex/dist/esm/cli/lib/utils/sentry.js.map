{"version": 3, "sources": ["../../../../../src/cli/lib/utils/sentry.ts"], "sourcesContent": ["import \"@sentry/tracing\";\nimport { productionProvisionHost, provisionHost } from \"../config.js\";\nimport stripAnsi from \"strip-ansi\";\nimport * as Sentry from \"@sentry/node\";\nimport { version } from \"../../../index.js\";\n\nexport const SENTRY_DSN =\n  \"https://<EMAIL>/6390839\";\n\nexport function initSentry() {\n  if (!process.env.CI && provisionHost === productionProvisionHost) {\n    Sentry.init({\n      dsn: SENTRY_DSN,\n      release: \"cli@\" + version,\n      tracesSampleRate: 0.2,\n      beforeBreadcrumb: (breadcrumb) => {\n        // Strip ANSI color codes from log lines that are sent as breadcrumbs.\n        if (breadcrumb.message) {\n          breadcrumb.message = stripAnsi(breadcrumb.message);\n        }\n        return breadcrumb;\n      },\n    });\n  }\n}\n"], "mappings": ";AAAA,OAAO;AACP,SAAS,yBAAyB,qBAAqB;AACvD,OAAO,eAAe;AACtB,YAAY,YAAY;AACxB,SAAS,eAAe;AAEjB,aAAM,aACX;AAEK,gBAAS,aAAa;AAC3B,MAAI,CAAC,QAAQ,IAAI,MAAM,kBAAkB,yBAAyB;AAChE,WAAO,KAAK;AAAA,MACV,KAAK;AAAA,MACL,SAAS,SAAS;AAAA,MAClB,kBAAkB;AAAA,MAClB,kBAAkB,CAAC,eAAe;AAEhC,YAAI,WAAW,SAAS;AACtB,qBAAW,UAAU,UAAU,WAAW,OAAO;AAAA,QACnD;AACA,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH;AACF;", "names": []}
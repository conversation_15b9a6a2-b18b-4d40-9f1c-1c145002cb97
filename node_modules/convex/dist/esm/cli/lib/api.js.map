{"version": 3, "sources": ["../../../../src/cli/lib/api.ts"], "sourcesContent": ["import { Context, logVerbose, logWarning } from \"../../bundler/context.js\";\nimport { getTeamAndProjectFromPreviewAdminKey } from \"./deployment.js\";\nimport {\n  assertLocalBackendRunning,\n  localDeploymentUrl,\n} from \"./localDeployment/run.js\";\nimport {\n  ThrowingFetchError,\n  bigBrainAPI,\n  bigBrainAPIMaybeThrows,\n  logAndHandleFetchError,\n} from \"./utils/utils.js\";\nimport { z } from \"zod\";\nimport {\n  DeploymentSelection,\n  ProjectSelection,\n} from \"./deploymentSelection.js\";\nimport { loadLocalDeploymentCredentials } from \"./localDeployment/localDeployment.js\";\nimport { loadAnonymousDeployment } from \"./localDeployment/anonymous.js\";\nexport type DeploymentName = string;\nexport type CloudDeploymentType = \"prod\" | \"dev\" | \"preview\";\nexport type AccountRequiredDeploymentType = CloudDeploymentType | \"local\";\nexport type DeploymentType = AccountRequiredDeploymentType | \"anonymous\";\n\nexport type Project = {\n  id: string;\n  name: string;\n  slug: string;\n  isDemo: boolean;\n};\n\ntype AdminKey = string;\n\n// Provision a new project, creating a deployment of type `deploymentTypeToProvision`\nexport async function createProject(\n  ctx: Context,\n  {\n    teamSlug: selectedTeamSlug,\n    projectName,\n    partitionId,\n    deploymentTypeToProvision,\n  }: {\n    teamSlug: string;\n    projectName: string;\n    partitionId?: number;\n    deploymentTypeToProvision: \"prod\" | \"dev\";\n  },\n): Promise<{\n  projectSlug: string;\n  teamSlug: string;\n  projectsRemaining: number;\n}> {\n  const provisioningArgs = {\n    team: selectedTeamSlug,\n    projectName,\n    // TODO: Consider allowing projects with no deployments, or consider switching\n    // to provisioning prod on creation.\n    deploymentType: deploymentTypeToProvision,\n    partitionId,\n  };\n  const data = await bigBrainAPI({\n    ctx,\n    method: \"POST\",\n    url: \"create_project\",\n    data: provisioningArgs,\n  });\n  const { projectSlug, teamSlug, projectsRemaining } = data;\n  if (\n    projectSlug === undefined ||\n    teamSlug === undefined ||\n    projectsRemaining === undefined\n  ) {\n    const error =\n      \"Unexpected response during provisioning: \" + JSON.stringify(data);\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"transient\",\n      errForSentry: error,\n      printedMessage: error,\n    });\n  }\n  return {\n    projectSlug,\n    teamSlug,\n    projectsRemaining,\n  };\n}\n\n// ----------------------------------------------------------------------\n// Helpers for `deploymentSelectionFromOptions`\n// ----------------------------------------------------------------------\n\nexport const deploymentSelectionWithinProjectSchema = z.discriminatedUnion(\n  \"kind\",\n  [\n    z.object({ kind: z.literal(\"previewName\"), previewName: z.string() }),\n    z.object({ kind: z.literal(\"deploymentName\"), deploymentName: z.string() }),\n    z.object({ kind: z.literal(\"prod\"), partitionId: z.number().optional() }),\n    z.object({\n      kind: z.literal(\"implicitProd\"),\n      partitionId: z.number().optional(),\n    }),\n    z.object({ kind: z.literal(\"ownDev\"), partitionId: z.number().optional() }),\n  ],\n);\n\nexport type DeploymentSelectionWithinProject = z.infer<\n  typeof deploymentSelectionWithinProjectSchema\n>;\n\ntype DeploymentSelectionOptionsWithinProject = {\n  prod?: boolean | undefined;\n  // Whether this command defaults to prod when no other flags are provided. If\n  // this is not set, the default will be \"ownDev\"\n  implicitProd?: boolean;\n\n  previewName?: string | undefined;\n  deploymentName?: string | undefined;\n  partitionId?: string | undefined;\n};\n\nexport type DeploymentSelectionOptions =\n  DeploymentSelectionOptionsWithinProject & {\n    url?: string | undefined;\n    adminKey?: string | undefined;\n    envFile?: string | undefined;\n  };\n\nexport async function deploymentSelectionWithinProjectFromOptions(\n  ctx: Context,\n  options: DeploymentSelectionOptions,\n): Promise<DeploymentSelectionWithinProject> {\n  if (options.previewName !== undefined) {\n    return { kind: \"previewName\", previewName: options.previewName };\n  }\n  if (options.deploymentName !== undefined) {\n    return { kind: \"deploymentName\", deploymentName: options.deploymentName };\n  }\n  const partitionId = options.partitionId\n    ? parseInt(options.partitionId)\n    : undefined;\n  if (options.prod) {\n    return { kind: \"prod\", partitionId };\n  }\n  if (options.implicitProd) {\n    return { kind: \"implicitProd\", partitionId };\n  }\n  return { kind: \"ownDev\", partitionId };\n}\n\nexport async function validateDeploymentSelectionForExistingDeployment(\n  ctx: Context,\n  deploymentSelection: DeploymentSelectionWithinProject,\n  source: \"selfHosted\" | \"deployKey\" | \"cliArgs\",\n) {\n  if (\n    deploymentSelection.kind === \"ownDev\" ||\n    deploymentSelection.kind === \"implicitProd\"\n  ) {\n    // These are both considered the \"default\" selection depending on the command, so this is always fine\n    return;\n  }\n  switch (source) {\n    case \"selfHosted\":\n      return await ctx.crash({\n        exitCode: 1,\n        errorType: \"fatal\",\n        printedMessage:\n          \"The `--prod`, `--preview-name`, and `--deployment-name` flags cannot be used with a self-hosted deployment.\",\n      });\n    case \"deployKey\":\n      logWarning(\n        ctx,\n        \"Ignoring `--prod`, `--preview-name`, or `--deployment-name` flags and using deployment from CONVEX_DEPLOY_KEY\",\n      );\n      break;\n    case \"cliArgs\":\n      logWarning(\n        ctx,\n        \"Ignoring `--prod`, `--preview-name`, or `--deployment-name` flags since this command was run with --url and --admin-key\",\n      );\n      break;\n  }\n}\n\n// ----------------------------------------------------------------------\n// Helpers for `checkAccessToSelectedProject`\n// ----------------------------------------------------------------------\n\nasync function hasAccessToProject(\n  ctx: Context,\n  selector: { projectSlug: string; teamSlug: string },\n): Promise<boolean> {\n  try {\n    await bigBrainAPIMaybeThrows({\n      ctx,\n      url: `/api/teams/${selector.teamSlug}/projects/${selector.projectSlug}/deployments`,\n      method: \"GET\",\n    });\n    return true;\n  } catch (err) {\n    if (\n      err instanceof ThrowingFetchError &&\n      (err.serverErrorData?.code === \"TeamNotFound\" ||\n        err.serverErrorData?.code === \"ProjectNotFound\")\n    ) {\n      return false;\n    }\n    return logAndHandleFetchError(ctx, err);\n  }\n}\n\nexport async function checkAccessToSelectedProject(\n  ctx: Context,\n  projectSelection: ProjectSelection,\n): Promise<\n  | { kind: \"hasAccess\"; teamSlug: string; projectSlug: string }\n  | { kind: \"noAccess\" }\n  | { kind: \"unknown\" }\n> {\n  switch (projectSelection.kind) {\n    case \"deploymentName\": {\n      const result = await getTeamAndProjectSlugForDeployment(ctx, {\n        deploymentName: projectSelection.deploymentName,\n      });\n      if (result === null) {\n        return { kind: \"noAccess\" };\n      }\n      return {\n        kind: \"hasAccess\",\n        teamSlug: result.teamSlug,\n        projectSlug: result.projectSlug,\n      };\n    }\n    case \"teamAndProjectSlugs\": {\n      const hasAccess = await hasAccessToProject(ctx, {\n        teamSlug: projectSelection.teamSlug,\n        projectSlug: projectSelection.projectSlug,\n      });\n      if (!hasAccess) {\n        return { kind: \"noAccess\" };\n      }\n      return {\n        kind: \"hasAccess\",\n        teamSlug: projectSelection.teamSlug,\n        projectSlug: projectSelection.projectSlug,\n      };\n    }\n    case \"projectDeployKey\":\n      // Ideally we would be able to do an explicit check here, but if the key is invalid,\n      // it will instead fail as soon as we try to use the key.\n      return { kind: \"unknown\" };\n    default: {\n      const _exhaustivenessCheck: never = projectSelection;\n      return await ctx.crash({\n        exitCode: 1,\n        errorType: \"fatal\",\n        printedMessage: `Invalid project selection: ${(projectSelection as any).kind}`,\n      });\n    }\n  }\n}\n\nasync function getTeamAndProjectSlugForDeployment(\n  ctx: Context,\n  selector: { deploymentName: string },\n): Promise<{ teamSlug: string; projectSlug: string } | null> {\n  try {\n    const body = await bigBrainAPIMaybeThrows({\n      ctx,\n      url: `/api/deployment/${selector.deploymentName}/team_and_project`,\n      method: \"GET\",\n    });\n    return { teamSlug: body.team, projectSlug: body.project };\n  } catch (err) {\n    if (\n      err instanceof ThrowingFetchError &&\n      (err.serverErrorData?.code === \"DeploymentNotFound\" ||\n        err.serverErrorData?.code === \"ProjectNotFound\")\n    ) {\n      return null;\n    }\n    return logAndHandleFetchError(ctx, err);\n  }\n}\n\n// ----------------------------------------------------------------------\n// Helpers for fetching deployment credentials\n// ----------------------------------------------------------------------\n\n// Used by dev for upgrade from team and project in convex.json to CONVEX_DEPLOYMENT\nexport async function fetchDeploymentCredentialsProvisioningDevOrProdMaybeThrows(\n  ctx: Context,\n  projectSelection:\n    | { kind: \"teamAndProjectSlugs\"; teamSlug: string; projectSlug: string }\n    | { kind: \"projectDeployKey\"; projectDeployKey: string },\n  deploymentType: \"prod\" | \"dev\",\n  partitionId: number | undefined,\n): Promise<{\n  deploymentName: string;\n  deploymentUrl: string;\n  adminKey: AdminKey;\n}> {\n  if (projectSelection.kind === \"projectDeployKey\") {\n    const auth = ctx.bigBrainAuth();\n    const doesAuthMatch =\n      auth !== null &&\n      auth.kind === \"projectKey\" &&\n      auth.projectKey === projectSelection.projectDeployKey;\n    if (!doesAuthMatch) {\n      return await ctx.crash({\n        exitCode: 1,\n        errorType: \"fatal\",\n        errForSentry: new Error(\n          \"Expected project deploy key to match the big brain auth header\",\n        ),\n        printedMessage: \"Unexpected error when loading the Convex deployment\",\n      });\n    }\n  }\n  let data;\n  try {\n    data = await bigBrainAPIMaybeThrows({\n      ctx,\n      method: \"POST\",\n      url: \"deployment/provision_and_authorize\",\n      data: {\n        teamSlug:\n          projectSelection.kind === \"teamAndProjectSlugs\"\n            ? projectSelection.teamSlug\n            : null,\n        projectSlug:\n          projectSelection.kind === \"teamAndProjectSlugs\"\n            ? projectSelection.projectSlug\n            : null,\n        deploymentType: deploymentType === \"prod\" ? \"prod\" : \"dev\",\n        partitionId,\n      },\n    });\n  } catch (error) {\n    const msg = \"Unknown error during authorization: \" + error;\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"transient\",\n      errForSentry: new Error(msg),\n      printedMessage: msg,\n    });\n  }\n  const adminKey = data.adminKey;\n  const url = data.url;\n  const deploymentName = data.deploymentName;\n  if (adminKey === undefined || url === undefined) {\n    const msg = \"Unknown error during authorization: \" + JSON.stringify(data);\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"transient\",\n      errForSentry: new Error(msg),\n      printedMessage: msg,\n    });\n  }\n  return { adminKey, deploymentUrl: url, deploymentName };\n}\n\nasync function fetchExistingDevDeploymentCredentialsOrCrash(\n  ctx: Context,\n  deploymentName: DeploymentName,\n): Promise<{\n  deploymentName: string;\n  adminKey: string;\n  url: string;\n  deploymentType: DeploymentType;\n}> {\n  const slugs = await fetchTeamAndProject(ctx, deploymentName);\n  const credentials =\n    await fetchDeploymentCredentialsProvisioningDevOrProdMaybeThrows(\n      ctx,\n      {\n        kind: \"teamAndProjectSlugs\",\n        teamSlug: slugs.team,\n        projectSlug: slugs.project,\n      },\n      \"dev\",\n      undefined,\n    );\n  return {\n    deploymentName: credentials.deploymentName,\n    adminKey: credentials.adminKey,\n    url: credentials.deploymentUrl,\n    deploymentType: \"dev\",\n  };\n}\n\n// ----------------------------------------------------------------------\n// Helpers for `loadSelectedDeploymentCredentials`\n// ----------------------------------------------------------------------\n\nasync function handleOwnDev(\n  ctx: Context,\n  projectSelection: ProjectSelection,\n  partitionId: number | undefined,\n): Promise<{\n  deploymentName: string;\n  adminKey: string;\n  url: string;\n  deploymentType: DeploymentType;\n}> {\n  switch (projectSelection.kind) {\n    case \"deploymentName\": {\n      if (projectSelection.deploymentType === \"local\") {\n        const credentials = await loadLocalDeploymentCredentials(\n          ctx,\n          projectSelection.deploymentName,\n        );\n        return {\n          deploymentName: projectSelection.deploymentName,\n          adminKey: credentials.adminKey,\n          url: credentials.deploymentUrl,\n          deploymentType: \"local\",\n        };\n      }\n      return await fetchExistingDevDeploymentCredentialsOrCrash(\n        ctx,\n        projectSelection.deploymentName,\n      );\n    }\n    case \"teamAndProjectSlugs\":\n    case \"projectDeployKey\": {\n      // Note -- this provisions a dev deployment if one doesn't exist\n      const credentials =\n        await fetchDeploymentCredentialsProvisioningDevOrProdMaybeThrows(\n          ctx,\n          projectSelection,\n          \"dev\",\n          partitionId,\n        );\n      return {\n        url: credentials.deploymentUrl,\n        adminKey: credentials.adminKey,\n        deploymentName: credentials.deploymentName,\n        deploymentType: \"dev\",\n      };\n    }\n  }\n}\n\nasync function handleProd(\n  ctx: Context,\n  projectSelection: ProjectSelection,\n  partitionId: number | undefined,\n): Promise<{\n  deploymentName: string;\n  adminKey: string;\n  url: string;\n  deploymentType: \"prod\";\n}> {\n  switch (projectSelection.kind) {\n    case \"deploymentName\": {\n      const credentials = await bigBrainAPI({\n        ctx,\n        method: \"POST\",\n        url: \"deployment/authorize_prod\",\n        data: {\n          deploymentName: projectSelection.deploymentName,\n          partitionId: partitionId,\n        },\n      });\n      return credentials;\n    }\n    case \"teamAndProjectSlugs\":\n    case \"projectDeployKey\": {\n      const credentials =\n        await fetchDeploymentCredentialsProvisioningDevOrProdMaybeThrows(\n          ctx,\n          projectSelection,\n          \"prod\",\n          partitionId,\n        );\n      return {\n        url: credentials.deploymentUrl,\n        adminKey: credentials.adminKey,\n        deploymentName: credentials.deploymentName,\n        deploymentType: \"prod\",\n      };\n    }\n  }\n}\n\nasync function handlePreview(\n  ctx: Context,\n  previewName: string,\n  projectSelection: ProjectSelection,\n): Promise<{\n  deploymentName: string;\n  adminKey: string;\n  url: string;\n  deploymentType: \"preview\";\n}> {\n  switch (projectSelection.kind) {\n    case \"deploymentName\":\n    case \"teamAndProjectSlugs\":\n      return await bigBrainAPI({\n        ctx,\n        method: \"POST\",\n        url: \"deployment/authorize_preview\",\n        data: {\n          previewName: previewName,\n          projectSelection: projectSelection,\n        },\n      });\n\n    case \"projectDeployKey\":\n      // TODO -- this should be supported\n      return await ctx.crash({\n        exitCode: 1,\n        errorType: \"fatal\",\n        printedMessage:\n          \"Project deploy keys are not supported for preview deployments\",\n      });\n  }\n}\n\nasync function handleDeploymentName(\n  ctx: Context,\n  deploymentName: string,\n  projectSelection: ProjectSelection,\n): Promise<{\n  deploymentName: string;\n  adminKey: string;\n  url: string;\n  deploymentType: DeploymentType;\n}> {\n  switch (projectSelection.kind) {\n    case \"deploymentName\":\n    case \"teamAndProjectSlugs\":\n      return await bigBrainAPI({\n        ctx,\n        method: \"POST\",\n        url: \"deployment/authorize_within_current_project\",\n        data: {\n          selectedDeploymentName: deploymentName,\n          projectSelection: projectSelection,\n        },\n      });\n    case \"projectDeployKey\":\n      // TODO -- this should be supported\n      return await ctx.crash({\n        exitCode: 1,\n        errorType: \"fatal\",\n        printedMessage:\n          \"Project deploy keys are not supported with the --deployment-name flag\",\n      });\n  }\n}\n\nasync function fetchDeploymentCredentialsWithinCurrentProject(\n  ctx: Context,\n  projectSelection: ProjectSelection,\n  deploymentSelection: DeploymentSelectionWithinProject,\n): Promise<{\n  deploymentName: string;\n  adminKey: string;\n  url: string;\n  deploymentType: DeploymentType;\n}> {\n  switch (deploymentSelection.kind) {\n    case \"ownDev\": {\n      return await handleOwnDev(\n        ctx,\n        projectSelection,\n        deploymentSelection.partitionId,\n      );\n    }\n    case \"implicitProd\":\n    case \"prod\": {\n      return await handleProd(\n        ctx,\n        projectSelection,\n        deploymentSelection.partitionId,\n      );\n    }\n    case \"previewName\":\n      return await handlePreview(\n        ctx,\n        deploymentSelection.previewName,\n        projectSelection,\n      );\n    case \"deploymentName\":\n      return await handleDeploymentName(\n        ctx,\n        deploymentSelection.deploymentName,\n        projectSelection,\n      );\n    default: {\n      const _exhaustivenessCheck: never = deploymentSelection;\n      return ctx.crash({\n        exitCode: 1,\n        errorType: \"fatal\",\n        // This should be unreachable, so don't bother with a printed message.\n        printedMessage: null,\n        errForSentry: `Unexpected deployment selection: ${deploymentSelection as any}`,\n      });\n    }\n  }\n}\n\nasync function _loadExistingDeploymentCredentialsForProject(\n  ctx: Context,\n  targetProject: ProjectSelection,\n  deploymentSelection: DeploymentSelectionWithinProject,\n  { ensureLocalRunning } = { ensureLocalRunning: true },\n): Promise<{\n  adminKey: string;\n  url: string;\n  deploymentFields: {\n    deploymentName: string;\n    deploymentType: DeploymentType;\n    projectSlug: string | null;\n    teamSlug: string | null;\n  } | null;\n}> {\n  const accessResult = await checkAccessToSelectedProject(ctx, targetProject);\n  if (accessResult.kind === \"noAccess\") {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      printedMessage:\n        \"You don't have access to the selected project. Run `npx convex dev` to select a different project.\",\n    });\n  }\n  const result = await fetchDeploymentCredentialsWithinCurrentProject(\n    ctx,\n    targetProject,\n    deploymentSelection,\n  );\n  logVerbose(\n    ctx,\n    `Deployment URL: ${result.url}, Deployment Name: ${result.deploymentName}, Deployment Type: ${result.deploymentType}`,\n  );\n  if (ensureLocalRunning && result.deploymentType === \"local\") {\n    await assertLocalBackendRunning(ctx, {\n      url: result.url,\n      deploymentName: result.deploymentName,\n    });\n  }\n  return {\n    ...result,\n    deploymentFields: {\n      deploymentName: result.deploymentName,\n      deploymentType: result.deploymentType,\n\n      projectSlug:\n        accessResult.kind === \"hasAccess\" ? accessResult.projectSlug : null,\n      teamSlug:\n        accessResult.kind === \"hasAccess\" ? accessResult.teamSlug : null,\n    },\n  };\n}\n// This is used by most commands (notably not `dev` and `deploy`) to determine\n// which deployment to act on, taking into account the deployment selection flags.\n//\nexport async function loadSelectedDeploymentCredentials(\n  ctx: Context,\n  deploymentSelection: DeploymentSelection,\n  selectionWithinProject: DeploymentSelectionWithinProject,\n  { ensureLocalRunning } = { ensureLocalRunning: true },\n): Promise<{\n  adminKey: string;\n  url: string;\n  deploymentFields: {\n    deploymentName: string;\n    deploymentType: DeploymentType;\n    projectSlug: string | null;\n    teamSlug: string | null;\n  } | null;\n}> {\n  switch (deploymentSelection.kind) {\n    case \"existingDeployment\":\n      await validateDeploymentSelectionForExistingDeployment(\n        ctx,\n        selectionWithinProject,\n        deploymentSelection.deploymentToActOn.source,\n      );\n      // We're already set up.\n      logVerbose(\n        ctx,\n        `Deployment URL: ${deploymentSelection.deploymentToActOn.url}, Deployment Name: ${deploymentSelection.deploymentToActOn.deploymentFields?.deploymentName ?? \"unknown\"}, Deployment Type: ${deploymentSelection.deploymentToActOn.deploymentFields?.deploymentType ?? \"unknown\"}`,\n      );\n      return {\n        adminKey: deploymentSelection.deploymentToActOn.adminKey,\n        url: deploymentSelection.deploymentToActOn.url,\n        deploymentFields:\n          deploymentSelection.deploymentToActOn.deploymentFields,\n      };\n    case \"chooseProject\":\n      return await ctx.crash({\n        exitCode: 1,\n        errorType: \"fatal\",\n        printedMessage:\n          \"No CONVEX_DEPLOYMENT set, run `npx convex dev` to configure a Convex project\",\n      });\n    case \"preview\": {\n      const slugs = await getTeamAndProjectFromPreviewAdminKey(\n        ctx,\n        deploymentSelection.previewDeployKey,\n      );\n      return await _loadExistingDeploymentCredentialsForProject(\n        ctx,\n        {\n          kind: \"teamAndProjectSlugs\",\n          teamSlug: slugs.teamSlug,\n          projectSlug: slugs.projectSlug,\n        },\n        selectionWithinProject,\n        { ensureLocalRunning },\n      );\n    }\n    case \"deploymentWithinProject\": {\n      return await _loadExistingDeploymentCredentialsForProject(\n        ctx,\n        deploymentSelection.targetProject,\n        selectionWithinProject,\n        { ensureLocalRunning },\n      );\n    }\n    case \"anonymous\": {\n      if (deploymentSelection.deploymentName === null) {\n        return await ctx.crash({\n          exitCode: 1,\n          errorType: \"fatal\",\n          printedMessage:\n            \"No CONVEX_DEPLOYMENT set, run `npx convex dev` to configure a Convex project\",\n        });\n      }\n      const config = await loadAnonymousDeployment(\n        ctx,\n        deploymentSelection.deploymentName,\n      );\n      const url = localDeploymentUrl(config.ports.cloud);\n      if (ensureLocalRunning) {\n        await assertLocalBackendRunning(ctx, {\n          url,\n          deploymentName: deploymentSelection.deploymentName,\n        });\n      }\n      return {\n        adminKey: config.adminKey,\n        url,\n        deploymentFields: {\n          deploymentName: deploymentSelection.deploymentName,\n          deploymentType: \"anonymous\",\n          projectSlug: null,\n          teamSlug: null,\n        },\n      };\n    }\n    default: {\n      const _exhaustivenessCheck: never = deploymentSelection;\n      return await ctx.crash({\n        exitCode: 1,\n        errorType: \"fatal\",\n        printedMessage: \"Unknown deployment type\",\n      });\n    }\n  }\n}\n\nexport async function fetchTeamAndProject(\n  ctx: Context,\n  deploymentName: string,\n) {\n  const data = (await bigBrainAPI({\n    ctx,\n    method: \"GET\",\n    url: `deployment/${deploymentName}/team_and_project`,\n  })) as {\n    team: string; // slug\n    project: string; // slug\n    teamId: number;\n    projectId: number;\n  };\n\n  const { team, project } = data;\n  if (team === undefined || project === undefined) {\n    const msg =\n      \"Unknown error when fetching team and project: \" + JSON.stringify(data);\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"transient\",\n      errForSentry: new Error(msg),\n      printedMessage: msg,\n    });\n  }\n\n  return data;\n}\n\nexport async function fetchTeamAndProjectForKey(\n  ctx: Context,\n  // Deployment deploy key, like `prod:happy-animal-123|<stuff>`\n  deployKey: string,\n) {\n  const data = (await bigBrainAPI({\n    ctx,\n    method: \"POST\",\n    url: `deployment/team_and_project_for_key`,\n    data: {\n      deployKey: deployKey,\n    },\n  })) as {\n    team: string; // slug\n    project: string; // slug\n    teamId: number;\n    projectId: number;\n  };\n\n  const { team, project } = data;\n  if (team === undefined || project === undefined) {\n    const msg =\n      \"Unknown error when fetching team and project: \" + JSON.stringify(data);\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"transient\",\n      errForSentry: new Error(msg),\n      printedMessage: msg,\n    });\n  }\n\n  return data;\n}\n"], "mappings": ";AAAA,SAAkB,YAAY,kBAAkB;AAChD,SAAS,4CAA4C;AACrD;AAAA,EACE;AAAA,EACA;AAAA,OACK;AACP;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AACP,SAAS,SAAS;AAKlB,SAAS,sCAAsC;AAC/C,SAAS,+BAA+B;AAgBxC,sBAAsB,cACpB,KACA;AAAA,EACE,UAAU;AAAA,EACV;AAAA,EACA;AAAA,EACA;AACF,GAUC;AACD,QAAM,mBAAmB;AAAA,IACvB,MAAM;AAAA,IACN;AAAA;AAAA;AAAA,IAGA,gBAAgB;AAAA,IAChB;AAAA,EACF;AACA,QAAM,OAAO,MAAM,YAAY;AAAA,IAC7B;AAAA,IACA,QAAQ;AAAA,IACR,KAAK;AAAA,IACL,MAAM;AAAA,EACR,CAAC;AACD,QAAM,EAAE,aAAa,UAAU,kBAAkB,IAAI;AACrD,MACE,gBAAgB,UAChB,aAAa,UACb,sBAAsB,QACtB;AACA,UAAM,QACJ,8CAA8C,KAAK,UAAU,IAAI;AACnE,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,cAAc;AAAA,MACd,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAMO,aAAM,yCAAyC,EAAE;AAAA,EACtD;AAAA,EACA;AAAA,IACE,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,aAAa,GAAG,aAAa,EAAE,OAAO,EAAE,CAAC;AAAA,IACpE,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,gBAAgB,GAAG,gBAAgB,EAAE,OAAO,EAAE,CAAC;AAAA,IAC1E,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,MAAM,GAAG,aAAa,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;AAAA,IACxE,EAAE,OAAO;AAAA,MACP,MAAM,EAAE,QAAQ,cAAc;AAAA,MAC9B,aAAa,EAAE,OAAO,EAAE,SAAS;AAAA,IACnC,CAAC;AAAA,IACD,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,QAAQ,GAAG,aAAa,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;AAAA,EAC5E;AACF;AAwBA,sBAAsB,4CACpB,KACA,SAC2C;AAC3C,MAAI,QAAQ,gBAAgB,QAAW;AACrC,WAAO,EAAE,MAAM,eAAe,aAAa,QAAQ,YAAY;AAAA,EACjE;AACA,MAAI,QAAQ,mBAAmB,QAAW;AACxC,WAAO,EAAE,MAAM,kBAAkB,gBAAgB,QAAQ,eAAe;AAAA,EAC1E;AACA,QAAM,cAAc,QAAQ,cACxB,SAAS,QAAQ,WAAW,IAC5B;AACJ,MAAI,QAAQ,MAAM;AAChB,WAAO,EAAE,MAAM,QAAQ,YAAY;AAAA,EACrC;AACA,MAAI,QAAQ,cAAc;AACxB,WAAO,EAAE,MAAM,gBAAgB,YAAY;AAAA,EAC7C;AACA,SAAO,EAAE,MAAM,UAAU,YAAY;AACvC;AAEA,sBAAsB,iDACpB,KACA,qBACA,QACA;AACA,MACE,oBAAoB,SAAS,YAC7B,oBAAoB,SAAS,gBAC7B;AAEA;AAAA,EACF;AACA,UAAQ,QAAQ;AAAA,IACd,KAAK;AACH,aAAO,MAAM,IAAI,MAAM;AAAA,QACrB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBACE;AAAA,MACJ,CAAC;AAAA,IACH,KAAK;AACH;AAAA,QACE;AAAA,QACA;AAAA,MACF;AACA;AAAA,IACF,KAAK;AACH;AAAA,QACE;AAAA,QACA;AAAA,MACF;AACA;AAAA,EACJ;AACF;AAMA,eAAe,mBACb,KACA,UACkB;AAClB,MAAI;AACF,UAAM,uBAAuB;AAAA,MAC3B;AAAA,MACA,KAAK,cAAc,SAAS,QAAQ,aAAa,SAAS,WAAW;AAAA,MACrE,QAAQ;AAAA,IACV,CAAC;AACD,WAAO;AAAA,EACT,SAAS,KAAK;AACZ,QACE,eAAe,uBACd,IAAI,iBAAiB,SAAS,kBAC7B,IAAI,iBAAiB,SAAS,oBAChC;AACA,aAAO;AAAA,IACT;AACA,WAAO,uBAAuB,KAAK,GAAG;AAAA,EACxC;AACF;AAEA,sBAAsB,6BACpB,KACA,kBAKA;AACA,UAAQ,iBAAiB,MAAM;AAAA,IAC7B,KAAK,kBAAkB;AACrB,YAAM,SAAS,MAAM,mCAAmC,KAAK;AAAA,QAC3D,gBAAgB,iBAAiB;AAAA,MACnC,CAAC;AACD,UAAI,WAAW,MAAM;AACnB,eAAO,EAAE,MAAM,WAAW;AAAA,MAC5B;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,UAAU,OAAO;AAAA,QACjB,aAAa,OAAO;AAAA,MACtB;AAAA,IACF;AAAA,IACA,KAAK,uBAAuB;AAC1B,YAAM,YAAY,MAAM,mBAAmB,KAAK;AAAA,QAC9C,UAAU,iBAAiB;AAAA,QAC3B,aAAa,iBAAiB;AAAA,MAChC,CAAC;AACD,UAAI,CAAC,WAAW;AACd,eAAO,EAAE,MAAM,WAAW;AAAA,MAC5B;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,UAAU,iBAAiB;AAAA,QAC3B,aAAa,iBAAiB;AAAA,MAChC;AAAA,IACF;AAAA,IACA,KAAK;AAGH,aAAO,EAAE,MAAM,UAAU;AAAA,IAC3B,SAAS;AACP,YAAM,uBAA8B;AACpC,aAAO,MAAM,IAAI,MAAM;AAAA,QACrB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBAAgB,8BAA+B,iBAAyB,IAAI;AAAA,MAC9E,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAEA,eAAe,mCACb,KACA,UAC2D;AAC3D,MAAI;AACF,UAAM,OAAO,MAAM,uBAAuB;AAAA,MACxC;AAAA,MACA,KAAK,mBAAmB,SAAS,cAAc;AAAA,MAC/C,QAAQ;AAAA,IACV,CAAC;AACD,WAAO,EAAE,UAAU,KAAK,MAAM,aAAa,KAAK,QAAQ;AAAA,EAC1D,SAAS,KAAK;AACZ,QACE,eAAe,uBACd,IAAI,iBAAiB,SAAS,wBAC7B,IAAI,iBAAiB,SAAS,oBAChC;AACA,aAAO;AAAA,IACT;AACA,WAAO,uBAAuB,KAAK,GAAG;AAAA,EACxC;AACF;AAOA,sBAAsB,2DACpB,KACA,kBAGA,gBACA,aAKC;AACD,MAAI,iBAAiB,SAAS,oBAAoB;AAChD,UAAM,OAAO,IAAI,aAAa;AAC9B,UAAM,gBACJ,SAAS,QACT,KAAK,SAAS,gBACd,KAAK,eAAe,iBAAiB;AACvC,QAAI,CAAC,eAAe;AAClB,aAAO,MAAM,IAAI,MAAM;AAAA,QACrB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,cAAc,IAAI;AAAA,UAChB;AAAA,QACF;AAAA,QACA,gBAAgB;AAAA,MAClB,CAAC;AAAA,IACH;AAAA,EACF;AACA,MAAI;AACJ,MAAI;AACF,WAAO,MAAM,uBAAuB;AAAA,MAClC;AAAA,MACA,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,MAAM;AAAA,QACJ,UACE,iBAAiB,SAAS,wBACtB,iBAAiB,WACjB;AAAA,QACN,aACE,iBAAiB,SAAS,wBACtB,iBAAiB,cACjB;AAAA,QACN,gBAAgB,mBAAmB,SAAS,SAAS;AAAA,QACrD;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH,SAAS,OAAO;AACd,UAAM,MAAM,yCAAyC;AACrD,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,cAAc,IAAI,MAAM,GAAG;AAAA,MAC3B,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AACA,QAAM,WAAW,KAAK;AACtB,QAAM,MAAM,KAAK;AACjB,QAAM,iBAAiB,KAAK;AAC5B,MAAI,aAAa,UAAa,QAAQ,QAAW;AAC/C,UAAM,MAAM,yCAAyC,KAAK,UAAU,IAAI;AACxE,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,cAAc,IAAI,MAAM,GAAG;AAAA,MAC3B,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AACA,SAAO,EAAE,UAAU,eAAe,KAAK,eAAe;AACxD;AAEA,eAAe,6CACb,KACA,gBAMC;AACD,QAAM,QAAQ,MAAM,oBAAoB,KAAK,cAAc;AAC3D,QAAM,cACJ,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,MACE,MAAM;AAAA,MACN,UAAU,MAAM;AAAA,MAChB,aAAa,MAAM;AAAA,IACrB;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF,SAAO;AAAA,IACL,gBAAgB,YAAY;AAAA,IAC5B,UAAU,YAAY;AAAA,IACtB,KAAK,YAAY;AAAA,IACjB,gBAAgB;AAAA,EAClB;AACF;AAMA,eAAe,aACb,KACA,kBACA,aAMC;AACD,UAAQ,iBAAiB,MAAM;AAAA,IAC7B,KAAK,kBAAkB;AACrB,UAAI,iBAAiB,mBAAmB,SAAS;AAC/C,cAAM,cAAc,MAAM;AAAA,UACxB;AAAA,UACA,iBAAiB;AAAA,QACnB;AACA,eAAO;AAAA,UACL,gBAAgB,iBAAiB;AAAA,UACjC,UAAU,YAAY;AAAA,UACtB,KAAK,YAAY;AAAA,UACjB,gBAAgB;AAAA,QAClB;AAAA,MACF;AACA,aAAO,MAAM;AAAA,QACX;AAAA,QACA,iBAAiB;AAAA,MACnB;AAAA,IACF;AAAA,IACA,KAAK;AAAA,IACL,KAAK,oBAAoB;AAEvB,YAAM,cACJ,MAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACF,aAAO;AAAA,QACL,KAAK,YAAY;AAAA,QACjB,UAAU,YAAY;AAAA,QACtB,gBAAgB,YAAY;AAAA,QAC5B,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,EACF;AACF;AAEA,eAAe,WACb,KACA,kBACA,aAMC;AACD,UAAQ,iBAAiB,MAAM;AAAA,IAC7B,KAAK,kBAAkB;AACrB,YAAM,cAAc,MAAM,YAAY;AAAA,QACpC;AAAA,QACA,QAAQ;AAAA,QACR,KAAK;AAAA,QACL,MAAM;AAAA,UACJ,gBAAgB,iBAAiB;AAAA,UACjC;AAAA,QACF;AAAA,MACF,CAAC;AACD,aAAO;AAAA,IACT;AAAA,IACA,KAAK;AAAA,IACL,KAAK,oBAAoB;AACvB,YAAM,cACJ,MAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACF,aAAO;AAAA,QACL,KAAK,YAAY;AAAA,QACjB,UAAU,YAAY;AAAA,QACtB,gBAAgB,YAAY;AAAA,QAC5B,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,EACF;AACF;AAEA,eAAe,cACb,KACA,aACA,kBAMC;AACD,UAAQ,iBAAiB,MAAM;AAAA,IAC7B,KAAK;AAAA,IACL,KAAK;AACH,aAAO,MAAM,YAAY;AAAA,QACvB;AAAA,QACA,QAAQ;AAAA,QACR,KAAK;AAAA,QACL,MAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IAEH,KAAK;AAEH,aAAO,MAAM,IAAI,MAAM;AAAA,QACrB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBACE;AAAA,MACJ,CAAC;AAAA,EACL;AACF;AAEA,eAAe,qBACb,KACA,gBACA,kBAMC;AACD,UAAQ,iBAAiB,MAAM;AAAA,IAC7B,KAAK;AAAA,IACL,KAAK;AACH,aAAO,MAAM,YAAY;AAAA,QACvB;AAAA,QACA,QAAQ;AAAA,QACR,KAAK;AAAA,QACL,MAAM;AAAA,UACJ,wBAAwB;AAAA,UACxB;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH,KAAK;AAEH,aAAO,MAAM,IAAI,MAAM;AAAA,QACrB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBACE;AAAA,MACJ,CAAC;AAAA,EACL;AACF;AAEA,eAAe,+CACb,KACA,kBACA,qBAMC;AACD,UAAQ,oBAAoB,MAAM;AAAA,IAChC,KAAK,UAAU;AACb,aAAO,MAAM;AAAA,QACX;AAAA,QACA;AAAA,QACA,oBAAoB;AAAA,MACtB;AAAA,IACF;AAAA,IACA,KAAK;AAAA,IACL,KAAK,QAAQ;AACX,aAAO,MAAM;AAAA,QACX;AAAA,QACA;AAAA,QACA,oBAAoB;AAAA,MACtB;AAAA,IACF;AAAA,IACA,KAAK;AACH,aAAO,MAAM;AAAA,QACX;AAAA,QACA,oBAAoB;AAAA,QACpB;AAAA,MACF;AAAA,IACF,KAAK;AACH,aAAO,MAAM;AAAA,QACX;AAAA,QACA,oBAAoB;AAAA,QACpB;AAAA,MACF;AAAA,IACF,SAAS;AACP,YAAM,uBAA8B;AACpC,aAAO,IAAI,MAAM;AAAA,QACf,UAAU;AAAA,QACV,WAAW;AAAA;AAAA,QAEX,gBAAgB;AAAA,QAChB,cAAc,oCAAoC,mBAA0B;AAAA,MAC9E,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAEA,eAAe,6CACb,KACA,eACA,qBACA,EAAE,mBAAmB,IAAI,EAAE,oBAAoB,KAAK,GAUnD;AACD,QAAM,eAAe,MAAM,6BAA6B,KAAK,aAAa;AAC1E,MAAI,aAAa,SAAS,YAAY;AACpC,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBACE;AAAA,IACJ,CAAC;AAAA,EACH;AACA,QAAM,SAAS,MAAM;AAAA,IACnB;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA;AAAA,IACE;AAAA,IACA,mBAAmB,OAAO,GAAG,sBAAsB,OAAO,cAAc,sBAAsB,OAAO,cAAc;AAAA,EACrH;AACA,MAAI,sBAAsB,OAAO,mBAAmB,SAAS;AAC3D,UAAM,0BAA0B,KAAK;AAAA,MACnC,KAAK,OAAO;AAAA,MACZ,gBAAgB,OAAO;AAAA,IACzB,CAAC;AAAA,EACH;AACA,SAAO;AAAA,IACL,GAAG;AAAA,IACH,kBAAkB;AAAA,MAChB,gBAAgB,OAAO;AAAA,MACvB,gBAAgB,OAAO;AAAA,MAEvB,aACE,aAAa,SAAS,cAAc,aAAa,cAAc;AAAA,MACjE,UACE,aAAa,SAAS,cAAc,aAAa,WAAW;AAAA,IAChE;AAAA,EACF;AACF;AAIA,sBAAsB,kCACpB,KACA,qBACA,wBACA,EAAE,mBAAmB,IAAI,EAAE,oBAAoB,KAAK,GAUnD;AACD,UAAQ,oBAAoB,MAAM;AAAA,IAChC,KAAK;AACH,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA,oBAAoB,kBAAkB;AAAA,MACxC;AAEA;AAAA,QACE;AAAA,QACA,mBAAmB,oBAAoB,kBAAkB,GAAG,sBAAsB,oBAAoB,kBAAkB,kBAAkB,kBAAkB,SAAS,sBAAsB,oBAAoB,kBAAkB,kBAAkB,kBAAkB,SAAS;AAAA,MAChR;AACA,aAAO;AAAA,QACL,UAAU,oBAAoB,kBAAkB;AAAA,QAChD,KAAK,oBAAoB,kBAAkB;AAAA,QAC3C,kBACE,oBAAoB,kBAAkB;AAAA,MAC1C;AAAA,IACF,KAAK;AACH,aAAO,MAAM,IAAI,MAAM;AAAA,QACrB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBACE;AAAA,MACJ,CAAC;AAAA,IACH,KAAK,WAAW;AACd,YAAM,QAAQ,MAAM;AAAA,QAClB;AAAA,QACA,oBAAoB;AAAA,MACtB;AACA,aAAO,MAAM;AAAA,QACX;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,UAAU,MAAM;AAAA,UAChB,aAAa,MAAM;AAAA,QACrB;AAAA,QACA;AAAA,QACA,EAAE,mBAAmB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,KAAK,2BAA2B;AAC9B,aAAO,MAAM;AAAA,QACX;AAAA,QACA,oBAAoB;AAAA,QACpB;AAAA,QACA,EAAE,mBAAmB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,KAAK,aAAa;AAChB,UAAI,oBAAoB,mBAAmB,MAAM;AAC/C,eAAO,MAAM,IAAI,MAAM;AAAA,UACrB,UAAU;AAAA,UACV,WAAW;AAAA,UACX,gBACE;AAAA,QACJ,CAAC;AAAA,MACH;AACA,YAAM,SAAS,MAAM;AAAA,QACnB;AAAA,QACA,oBAAoB;AAAA,MACtB;AACA,YAAM,MAAM,mBAAmB,OAAO,MAAM,KAAK;AACjD,UAAI,oBAAoB;AACtB,cAAM,0BAA0B,KAAK;AAAA,UACnC;AAAA,UACA,gBAAgB,oBAAoB;AAAA,QACtC,CAAC;AAAA,MACH;AACA,aAAO;AAAA,QACL,UAAU,OAAO;AAAA,QACjB;AAAA,QACA,kBAAkB;AAAA,UAChB,gBAAgB,oBAAoB;AAAA,UACpC,gBAAgB;AAAA,UAChB,aAAa;AAAA,UACb,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,IACF;AAAA,IACA,SAAS;AACP,YAAM,uBAA8B;AACpC,aAAO,MAAM,IAAI,MAAM;AAAA,QACrB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBAAgB;AAAA,MAClB,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAEA,sBAAsB,oBACpB,KACA,gBACA;AACA,QAAM,OAAQ,MAAM,YAAY;AAAA,IAC9B;AAAA,IACA,QAAQ;AAAA,IACR,KAAK,cAAc,cAAc;AAAA,EACnC,CAAC;AAOD,QAAM,EAAE,MAAM,QAAQ,IAAI;AAC1B,MAAI,SAAS,UAAa,YAAY,QAAW;AAC/C,UAAM,MACJ,mDAAmD,KAAK,UAAU,IAAI;AACxE,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,cAAc,IAAI,MAAM,GAAG;AAAA,MAC3B,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AAEA,SAAO;AACT;AAEA,sBAAsB,0BACpB,KAEA,WACA;AACA,QAAM,OAAQ,MAAM,YAAY;AAAA,IAC9B;AAAA,IACA,QAAQ;AAAA,IACR,KAAK;AAAA,IACL,MAAM;AAAA,MACJ;AAAA,IACF;AAAA,EACF,CAAC;AAOD,QAAM,EAAE,MAAM,QAAQ,IAAI;AAC1B,MAAI,SAAS,UAAa,YAAY,QAAW;AAC/C,UAAM,MACJ,mDAAmD,KAAK,UAAU,IAAI;AACxE,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,cAAc,IAAI,MAAM,GAAG;AAAA,MAC3B,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AAEA,SAAO;AACT;", "names": []}
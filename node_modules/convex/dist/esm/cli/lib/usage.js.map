{"version": 3, "sources": ["../../../../src/cli/lib/usage.ts"], "sourcesContent": ["import chalk from \"chalk\";\nimport { Context, logWarning } from \"../../bundler/context.js\";\nimport { teamDashboardUrl } from \"./dashboard.js\";\nimport { fetchTeamAndProject } from \"./api.js\";\nimport { bigBrainAPI } from \"./utils/utils.js\";\n\nasync function warn(\n  ctx: Context,\n  options: { title: string; subtitle: string; teamSlug: string },\n) {\n  const { title, subtitle, teamSlug } = options;\n  logWarning(ctx, chalk.bold.yellow(title));\n  logWarning(ctx, chalk.yellow(subtitle));\n  logWarning(\n    ctx,\n    chalk.yellow(`Visit ${teamDashboardUrl(teamSlug)} to learn more.`),\n  );\n}\n\nasync function teamUsageState(ctx: Context, teamId: number) {\n  const { usageState } = (await bigBrainAPI({\n    ctx,\n    method: \"GET\",\n    url: \"dashboard/teams/\" + teamId + \"/usage/team_usage_state\",\n  })) as {\n    usageState: \"Default\" | \"Approaching\" | \"Exceeded\" | \"Disabled\" | \"Paused\";\n  };\n\n  return usageState;\n}\n\nasync function teamSpendingLimitsState(ctx: Context, teamId: number) {\n  const response = (await bigBrainAPI({\n    ctx,\n    method: \"GET\",\n    url: \"dashboard/teams/\" + teamId + \"/get_spending_limits\",\n  })) as {\n    disableThresholdCents: number | null;\n    state: null | \"Running\" | \"Disabled\" | \"Warning\";\n  };\n\n  return response.state;\n}\n\nexport async function usageStateWarning(\n  ctx: Context,\n  targetDeployment: string,\n) {\n  // Skip the warning if the user doesn’t have an auth token\n  // (which can happen for instance when using a deploy key)\n  const auth = ctx.bigBrainAuth();\n  if (auth === null || auth.kind === \"projectKey\") {\n    return;\n  }\n  const { teamId, team } = await fetchTeamAndProject(ctx, targetDeployment);\n\n  const [usageState, spendingLimitsState] = await Promise.all([\n    teamUsageState(ctx, teamId),\n    teamSpendingLimitsState(ctx, teamId),\n  ]);\n  if (spendingLimitsState === \"Disabled\") {\n    await warn(ctx, {\n      title:\n        \"Your projects are disabled because you exceeded your spending limit.\",\n      subtitle: \"Increase it from the dashboard to re-enable your projects.\",\n      teamSlug: team,\n    });\n  } else if (usageState === \"Approaching\") {\n    await warn(ctx, {\n      title: \"Your projects are approaching the Starter plan limits.\",\n      subtitle: \"Consider upgrading to avoid service interruption.\",\n      teamSlug: team,\n    });\n  } else if (usageState === \"Exceeded\") {\n    await warn(ctx, {\n      title: \"Your projects are above the Starter plan limits.\",\n      subtitle: \"Decrease your usage or upgrade to avoid service interruption.\",\n      teamSlug: team,\n    });\n  } else if (usageState === \"Disabled\") {\n    await warn(ctx, {\n      title:\n        \"Your projects are disabled because the team exceeded Starter plan limits.\",\n      subtitle: \"Decrease your usage or upgrade to reenable your projects.\",\n      teamSlug: team,\n    });\n  } else if (usageState === \"Paused\") {\n    await warn(ctx, {\n      title:\n        \"Your projects are disabled because the team previously exceeded Starter plan limits.\",\n      subtitle: \"Restore your projects by going to the dashboard.\",\n      teamSlug: team,\n    });\n  }\n}\n"], "mappings": ";AAAA,OAAO,WAAW;AAClB,SAAkB,kBAAkB;AACpC,SAAS,wBAAwB;AACjC,SAAS,2BAA2B;AACpC,SAAS,mBAAmB;AAE5B,eAAe,KACb,KACA,SACA;AACA,QAAM,EAAE,OAAO,UAAU,SAAS,IAAI;AACtC,aAAW,KAAK,MAAM,KAAK,OAAO,KAAK,CAAC;AACxC,aAAW,KAAK,MAAM,OAAO,QAAQ,CAAC;AACtC;AAAA,IACE;AAAA,IACA,MAAM,OAAO,SAAS,iBAAiB,QAAQ,CAAC,iBAAiB;AAAA,EACnE;AACF;AAEA,eAAe,eAAe,KAAc,QAAgB;AAC1D,QAAM,EAAE,WAAW,IAAK,MAAM,YAAY;AAAA,IACxC;AAAA,IACA,QAAQ;AAAA,IACR,KAAK,qBAAqB,SAAS;AAAA,EACrC,CAAC;AAID,SAAO;AACT;AAEA,eAAe,wBAAwB,KAAc,QAAgB;AACnE,QAAM,WAAY,MAAM,YAAY;AAAA,IAClC;AAAA,IACA,QAAQ;AAAA,IACR,KAAK,qBAAqB,SAAS;AAAA,EACrC,CAAC;AAKD,SAAO,SAAS;AAClB;AAEA,sBAAsB,kBACpB,KACA,kBACA;AAGA,QAAM,OAAO,IAAI,aAAa;AAC9B,MAAI,SAAS,QAAQ,KAAK,SAAS,cAAc;AAC/C;AAAA,EACF;AACA,QAAM,EAAE,QAAQ,KAAK,IAAI,MAAM,oBAAoB,KAAK,gBAAgB;AAExE,QAAM,CAAC,YAAY,mBAAmB,IAAI,MAAM,QAAQ,IAAI;AAAA,IAC1D,eAAe,KAAK,MAAM;AAAA,IAC1B,wBAAwB,KAAK,MAAM;AAAA,EACrC,CAAC;AACD,MAAI,wBAAwB,YAAY;AACtC,UAAM,KAAK,KAAK;AAAA,MACd,OACE;AAAA,MACF,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,WAAW,eAAe,eAAe;AACvC,UAAM,KAAK,KAAK;AAAA,MACd,OAAO;AAAA,MACP,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,WAAW,eAAe,YAAY;AACpC,UAAM,KAAK,KAAK;AAAA,MACd,OAAO;AAAA,MACP,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,WAAW,eAAe,YAAY;AACpC,UAAM,KAAK,KAAK;AAAA,MACd,OACE;AAAA,MACF,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,WAAW,eAAe,UAAU;AAClC,UAAM,KAAK,KAAK;AAAA,MACd,OACE;AAAA,MACF,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH;AACF;", "names": []}
"use strict";
import { header } from "./common.js";
export function importPath(modulePath) {
  const filePath = modulePath.replace(/\\/g, "/");
  const lastDot = filePath.lastIndexOf(".");
  return filePath.slice(0, lastDot === -1 ? void 0 : lastDot);
}
export function moduleIdentifier(modulePath) {
  let safeModulePath = importPath(modulePath).replace(/\//g, "_").replace(/-/g, "_");
  if (["fullApi", "api", "internal", "components"].includes(safeModulePath)) {
    safeModulePath = `${safeModulePath}_`;
  }
  return safeModulePath;
}
export function apiCodegen(modulePaths) {
  const apiDTS = `${header("Generated `api` utility.")}
  import type { ApiFromModules, FilterApi, FunctionReference } from "convex/server";
  ${modulePaths.map(
    (modulePath) => `import type * as ${moduleIdentifier(modulePath)} from "../${importPath(
      modulePath
    )}.js";`
  ).join("\n")}

  /**
   * A utility for referencing Convex functions in your app's API.
   *
   * Usage:
   * \`\`\`js
   * const myFunctionReference = api.myModule.myFunction;
   * \`\`\`
   */
  declare const fullApi: ApiFromModules<{
    ${modulePaths.map(
    (modulePath) => `"${importPath(modulePath)}": typeof ${moduleIdentifier(modulePath)},`
  ).join("\n")}
  }>;
  export declare const api: FilterApi<typeof fullApi, FunctionReference<any, "public">>;
  export declare const internal: FilterApi<typeof fullApi, FunctionReference<any, "internal">>;
  `;
  const apiJS = `${header("Generated `api` utility.")}
  import { anyApi } from "convex/server";

  /**
   * A utility for referencing Convex functions in your app's API.
   *
   * Usage:
   * \`\`\`js
   * const myFunctionReference = api.myModule.myFunction;
   * \`\`\`
   */
  export const api = anyApi;
  export const internal = anyApi;
  `;
  return {
    DTS: apiDTS,
    JS: apiJS
  };
}
//# sourceMappingURL=api.js.map

{"version": 3, "sources": ["../../../../src/cli/codegen_templates/readme.ts"], "sourcesContent": ["export function readmeCodegen() {\n  return `# Welcome to your Convex functions directory!\n\nWrite your Convex functions here.\nSee https://docs.convex.dev/functions for more.\n\nA query function that takes two arguments looks like:\n\n\\`\\`\\`ts\n// functions.js\nimport { query } from \"./_generated/server\";\nimport { v } from \"convex/values\";\n\nexport const myQueryFunction = query({\n  // Validators for arguments.\n  args: {\n    first: v.number(),\n    second: v.string(),\n  },\n\n  // Function implementation.\n  handler: async (ctx, args) => {\n    // Read the database as many times as you need here.\n    // See https://docs.convex.dev/database/reading-data.\n    const documents = await ctx.db.query(\"tablename\").collect();\n\n    // Arguments passed from the client are properties of the args object.\n    console.log(args.first, args.second)\n\n    // Write arbitrary JavaScript here: filter, aggregate, build derived data,\n    // remove non-public properties, or create new objects.\n    return documents;\n  },\n});\n\\`\\`\\`\n\nUsing this query function in a React component looks like:\n\n\\`\\`\\`ts\nconst data = useQuery(api.functions.myQueryFunction, { first: 10, second: \"hello\" });\n\\`\\`\\`\n\n\nA mutation function looks like:\n\n\\`\\`\\`ts\n// functions.js\nimport { mutation } from \"./_generated/server\";\nimport { v } from \"convex/values\";\n\nexport const myMutationFunction = mutation({\n  // Validators for arguments.\n  args: {\n    first: v.string(),\n    second: v.string(),\n  },\n\n  // Function implementation.\n  handler: async (ctx, args) => {\n    // Insert or modify documents in the database here.\n    // Mutations can also read from the database like queries.\n    // See https://docs.convex.dev/database/writing-data.\n    const message = { body: args.first, author: args.second };\n    const id = await ctx.db.insert(\"messages\", message);\n\n    // Optionally, return a value from your mutation.\n    return await ctx.db.get(id);\n  },\n});\n\\`\\`\\`\n\nUsing this mutation function in a React component looks like:\n\n\\`\\`\\`ts\nconst mutation = useMutation(api.functions.myMutationFunction);\nfunction handleButtonPress() {\n  // fire and forget, the most common way to use mutations\n  mutation({ first: \"Hello!\", second: \"me\" });\n  // OR\n  // use the result once the mutation has completed\n  mutation({ first: \"Hello!\", second: \"me\" }).then(result => console.log(result));\n}\n\\`\\`\\`\n\nUse the Convex CLI to push your functions to a deployment. See everything\nthe Convex CLI can do by running \\`npx convex -h\\` in your project root\ndirectory. To learn more, launch the docs with \\`npx convex docs\\`.\n`;\n}\n"], "mappings": ";AAAO,gBAAS,gBAAgB;AAC9B,SAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAuFT;", "names": []}
{"version": 3, "sources": ["../../../src/cli/index.ts"], "sourcesContent": ["import { Command } from \"@commander-js/extra-typings\";\nimport { init } from \"./init.js\";\nimport { dashboard } from \"./dashboard.js\";\nimport { deployments } from \"./deployments.js\";\nimport { docs } from \"./docs.js\";\nimport { run } from \"./run.js\";\nimport { version } from \"./version.js\";\nimport { auth } from \"./auth.js\";\nimport { codegen } from \"./codegen.js\";\nimport { reinit } from \"./reinit.js\";\nimport { update } from \"./update.js\";\nimport { typecheck } from \"./typecheck.js\";\nimport { login } from \"./login.js\";\nimport { logout } from \"./logout.js\";\nimport chalk from \"chalk\";\nimport * as Sentry from \"@sentry/node\";\nimport { initSentry } from \"./lib/utils/sentry.js\";\nimport { dev } from \"./dev.js\";\nimport { deploy } from \"./deploy.js\";\nimport { logs } from \"./logs.js\";\nimport { networkTest } from \"./network_test.js\";\nimport { convexExport } from \"./convexExport.js\";\nimport { convexImport } from \"./convexImport.js\";\nimport { env } from \"./env.js\";\nimport { data } from \"./data.js\";\nimport inquirer from \"inquirer\";\nimport inquirerSearchList from \"inquirer-search-list\";\nimport { format } from \"util\";\nimport { functionSpec } from \"./functionSpec.js\";\nimport { disableLocalDeployments } from \"./disableLocalDev.js\";\nimport { mcp } from \"./mcp.js\";\nimport dns from \"node:dns\";\n\nconst MINIMUM_MAJOR_VERSION = 16;\nconst MINIMUM_MINOR_VERSION = 15;\n\n// console.error before it started being red by default in Node.js v20\nfunction logToStderr(...args: unknown[]) {\n  process.stderr.write(`${format(...args)}\\n`);\n}\n\nasync function main() {\n  // Use ipv4 first for 127.0.0.1 in tests\n  dns.setDefaultResultOrder(\"ipv4first\");\n\n  initSentry();\n  inquirer.registerPrompt(\"search-list\", inquirerSearchList);\n\n  const nodeVersion = process.versions.node;\n  const majorVersion = parseInt(nodeVersion.split(\".\")[0], 10);\n  const minorVersion = parseInt(nodeVersion.split(\".\")[1], 10);\n  if (\n    majorVersion < MINIMUM_MAJOR_VERSION ||\n    (majorVersion === MINIMUM_MAJOR_VERSION &&\n      minorVersion < MINIMUM_MINOR_VERSION)\n  ) {\n    logToStderr(\n      chalk.red(\n        `Your Node version ${nodeVersion} is too old. Convex requires at least Node v${MINIMUM_MAJOR_VERSION}.${MINIMUM_MINOR_VERSION}`,\n      ),\n    );\n    logToStderr(\n      chalk.gray(\n        `You can use ${chalk.bold(\n          \"nvm\",\n        )} (https://github.com/nvm-sh/nvm#installing-and-updating) to manage different versions of Node.`,\n      ),\n    );\n    logToStderr(\n      chalk.gray(\n        \"After installing `nvm`, install the latest version of Node with \" +\n          chalk.bold(\"`nvm install node`.\"),\n      ),\n    );\n    logToStderr(\n      chalk.gray(\n        \"Then, activate the installed version in your terminal with \" +\n          chalk.bold(\"`nvm use`.\"),\n      ),\n    );\n    process.exit(1);\n  }\n\n  const program = new Command();\n  program\n    .name(\"convex\")\n    .usage(\"<command> [options]\")\n    .description(\"Start developing with Convex by running `npx convex dev`.\")\n    .addCommand(login, { hidden: true })\n    .addCommand(init, { hidden: true })\n    .addCommand(reinit, { hidden: true })\n    .addCommand(dev)\n    .addCommand(deploy)\n    .addCommand(deployments, { hidden: true })\n    .addCommand(run)\n    .addCommand(convexImport)\n    .addCommand(dashboard)\n    .addCommand(docs)\n    .addCommand(logs)\n    .addCommand(typecheck, { hidden: true })\n    .addCommand(auth, { hidden: true })\n    .addCommand(convexExport)\n    .addCommand(env)\n    .addCommand(data)\n    .addCommand(codegen)\n    .addCommand(update)\n    .addCommand(logout)\n    .addCommand(networkTest, { hidden: true })\n    .addCommand(functionSpec)\n    .addCommand(disableLocalDeployments)\n    .addCommand(mcp)\n    .addHelpCommand(\"help <command>\", \"Show help for given <command>\")\n    .version(version)\n    // Hide version and help so they don't clutter\n    // the list of commands.\n    .configureHelp({ visibleOptions: () => [] })\n    .showHelpAfterError();\n\n  // Run the command and be sure to flush Sentry before exiting.\n  try {\n    await program.parseAsync(process.argv);\n  } catch (e) {\n    Sentry.captureException(e);\n    process.exitCode = 1;\n    // This is too early to use `logError`, so just log directly.\n    // eslint-disable-next-line no-console\n    console.error(chalk.red(\"Unexpected Error: \" + e));\n  } finally {\n    await Sentry.close();\n  }\n  process.exit();\n}\nvoid main();\n"], "mappings": ";AAAA,SAAS,eAAe;AACxB,SAAS,YAAY;AACrB,SAAS,iBAAiB;AAC1B,SAAS,mBAAmB;AAC5B,SAAS,YAAY;AACrB,SAAS,WAAW;AACpB,SAAS,eAAe;AACxB,SAAS,YAAY;AACrB,SAAS,eAAe;AACxB,SAAS,cAAc;AACvB,SAAS,cAAc;AACvB,SAAS,iBAAiB;AAC1B,SAAS,aAAa;AACtB,SAAS,cAAc;AACvB,OAAO,WAAW;AAClB,YAAY,YAAY;AACxB,SAAS,kBAAkB;AAC3B,SAAS,WAAW;AACpB,SAAS,cAAc;AACvB,SAAS,YAAY;AACrB,SAAS,mBAAmB;AAC5B,SAAS,oBAAoB;AAC7B,SAAS,oBAAoB;AAC7B,SAAS,WAAW;AACpB,SAAS,YAAY;AACrB,OAAO,cAAc;AACrB,OAAO,wBAAwB;AAC/B,SAAS,cAAc;AACvB,SAAS,oBAAoB;AAC7B,SAAS,+BAA+B;AACxC,SAAS,WAAW;AACpB,OAAO,SAAS;AAEhB,MAAM,wBAAwB;AAC9B,MAAM,wBAAwB;AAG9B,SAAS,eAAe,MAAiB;AACvC,UAAQ,OAAO,MAAM,GAAG,OAAO,GAAG,IAAI,CAAC;AAAA,CAAI;AAC7C;AAEA,eAAe,OAAO;AAEpB,MAAI,sBAAsB,WAAW;AAErC,aAAW;AACX,WAAS,eAAe,eAAe,kBAAkB;AAEzD,QAAM,cAAc,QAAQ,SAAS;AACrC,QAAM,eAAe,SAAS,YAAY,MAAM,GAAG,EAAE,CAAC,GAAG,EAAE;AAC3D,QAAM,eAAe,SAAS,YAAY,MAAM,GAAG,EAAE,CAAC,GAAG,EAAE;AAC3D,MACE,eAAe,yBACd,iBAAiB,yBAChB,eAAe,uBACjB;AACA;AAAA,MACE,MAAM;AAAA,QACJ,qBAAqB,WAAW,+CAA+C,qBAAqB,IAAI,qBAAqB;AAAA,MAC/H;AAAA,IACF;AACA;AAAA,MACE,MAAM;AAAA,QACJ,eAAe,MAAM;AAAA,UACnB;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AACA;AAAA,MACE,MAAM;AAAA,QACJ,qEACE,MAAM,KAAK,qBAAqB;AAAA,MACpC;AAAA,IACF;AACA;AAAA,MACE,MAAM;AAAA,QACJ,gEACE,MAAM,KAAK,YAAY;AAAA,MAC3B;AAAA,IACF;AACA,YAAQ,KAAK,CAAC;AAAA,EAChB;AAEA,QAAM,UAAU,IAAI,QAAQ;AAC5B,UACG,KAAK,QAAQ,EACb,MAAM,qBAAqB,EAC3B,YAAY,2DAA2D,EACvE,WAAW,OAAO,EAAE,QAAQ,KAAK,CAAC,EAClC,WAAW,MAAM,EAAE,QAAQ,KAAK,CAAC,EACjC,WAAW,QAAQ,EAAE,QAAQ,KAAK,CAAC,EACnC,WAAW,GAAG,EACd,WAAW,MAAM,EACjB,WAAW,aAAa,EAAE,QAAQ,KAAK,CAAC,EACxC,WAAW,GAAG,EACd,WAAW,YAAY,EACvB,WAAW,SAAS,EACpB,WAAW,IAAI,EACf,WAAW,IAAI,EACf,WAAW,WAAW,EAAE,QAAQ,KAAK,CAAC,EACtC,WAAW,MAAM,EAAE,QAAQ,KAAK,CAAC,EACjC,WAAW,YAAY,EACvB,WAAW,GAAG,EACd,WAAW,IAAI,EACf,WAAW,OAAO,EAClB,WAAW,MAAM,EACjB,WAAW,MAAM,EACjB,WAAW,aAAa,EAAE,QAAQ,KAAK,CAAC,EACxC,WAAW,YAAY,EACvB,WAAW,uBAAuB,EAClC,WAAW,GAAG,EACd,eAAe,kBAAkB,+BAA+B,EAChE,QAAQ,OAAO,EAGf,cAAc,EAAE,gBAAgB,MAAM,CAAC,EAAE,CAAC,EAC1C,mBAAmB;AAGtB,MAAI;AACF,UAAM,QAAQ,WAAW,QAAQ,IAAI;AAAA,EACvC,SAAS,GAAG;AACV,WAAO,iBAAiB,CAAC;AACzB,YAAQ,WAAW;AAGnB,YAAQ,MAAM,MAAM,IAAI,uBAAuB,CAAC,CAAC;AAAA,EACnD,UAAE;AACA,UAAM,OAAO,MAAM;AAAA,EACrB;AACA,UAAQ,KAAK;AACf;AACA,KAAK,KAAK;", "names": []}
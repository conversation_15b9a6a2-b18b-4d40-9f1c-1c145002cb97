{"version": 3, "sources": ["../../../../src/browser/sync/client.ts"], "sourcesContent": ["import { version } from \"../../index.js\";\nimport { convexToJson, Value } from \"../../values/index.js\";\nimport {\n  createHybridErrorStacktrace,\n  forwardData,\n  instantiateDefaultLogger,\n  instantiateNoopLogger,\n  logFatalError,\n  Logger,\n} from \"../logging.js\";\nimport { LocalSyncState } from \"./local_state.js\";\nimport { RequestManager } from \"./request_manager.js\";\nimport {\n  OptimisticLocalStore,\n  OptimisticUpdate,\n} from \"./optimistic_updates.js\";\nimport {\n  OptimisticQueryResults,\n  QueryResultsMap,\n} from \"./optimistic_updates_impl.js\";\nimport {\n  ActionRequest,\n  MutationRequest,\n  QueryId,\n  QueryJournal,\n  RequestId,\n  ServerMessage,\n  TS,\n  UserIdentityAttributes,\n} from \"./protocol.js\";\nimport { RemoteQuerySet } from \"./remote_query_set.js\";\nimport { QueryToken, serializePathAndArgs } from \"./udf_path_utils.js\";\nimport { ReconnectMetadata, WebSocketManager } from \"./web_socket_manager.js\";\nimport { newSessionId } from \"./session.js\";\nimport { FunctionResult } from \"./function_result.js\";\nimport {\n  AuthenticationManager,\n  AuthTokenFetcher,\n} from \"./authentication_manager.js\";\nexport { type AuthTokenFetcher } from \"./authentication_manager.js\";\nimport { getMarksReport, mark, MarkName } from \"./metrics.js\";\nimport { parseArgs, validateDeploymentUrl } from \"../../common/index.js\";\nimport { ConvexError } from \"../../values/errors.js\";\n\n/**\n * Options for {@link BaseConvexClient}.\n *\n * @public\n */\nexport interface BaseConvexClientOptions {\n  /**\n   * Whether to prompt the user if they have unsaved changes pending\n   * when navigating away or closing a web page.\n   *\n   * This is only possible when the `window` object exists, i.e. in a browser.\n   *\n   * The default value is `true` in browsers.\n   */\n  unsavedChangesWarning?: boolean;\n  /**\n   * Specifies an alternate\n   * [WebSocket](https://developer.mozilla.org/en-US/docs/Web/API/WebSocket)\n   * constructor to use for client communication with the Convex cloud.\n   * The default behavior is to use `WebSocket` from the global environment.\n   */\n  webSocketConstructor?: typeof WebSocket;\n  /**\n   * Adds additional logging for debugging purposes.\n   *\n   * The default value is `false`.\n   */\n  verbose?: boolean;\n  /**\n   * A logger, `true`, or `false`. If not provided or `true`, logs to the console.\n   * If `false`, logs are not printed anywhere.\n   *\n   * You can construct your own logger to customize logging to log elsewhere.\n   */\n  logger?: Logger | boolean;\n  /**\n   * Sends additional metrics to Convex for debugging purposes.\n   *\n   * The default value is `false`.\n   */\n  reportDebugInfoToConvex?: boolean;\n  /**\n   * Skip validating that the Convex deployment URL looks like\n   * `https://happy-animal-123.convex.cloud` or localhost.\n   *\n   * This can be useful if running a self-hosted Convex backend that uses a different\n   * URL.\n   *\n   * The default value is `false`\n   */\n  skipConvexDeploymentUrlCheck?: boolean;\n  /**\n   * If using auth, the number of seconds before a token expires that we should refresh it.\n   *\n   * The default value is `2`.\n   */\n  authRefreshTokenLeewaySeconds?: number;\n}\n\n/**\n * State describing the client's connection with the Convex backend.\n *\n * @public\n */\nexport type ConnectionState = {\n  hasInflightRequests: boolean;\n  isWebSocketConnected: boolean;\n  timeOfOldestInflightRequest: Date | null;\n  /**\n   * True if the client has ever opened a WebSocket to the \"ready\" state.\n   */\n  hasEverConnected: boolean;\n  /**\n   * The number of times this client has connected to the Convex backend.\n   *\n   * A number of things can cause the client to reconnect -- server errors,\n   * bad internet, auth expiring. But this number being high is an indication\n   * that the client is having trouble keeping a stable connection.\n   */\n  connectionCount: number;\n  /**\n   * The number of times this client has tried (and failed) to connect to the Convex backend.\n   */\n  connectionRetries: number;\n  /**\n   * The number of mutations currently in flight.\n   */\n  inflightMutations: number;\n  /**\n   * The number of actions currently in flight.\n   */\n  inflightActions: number;\n};\n\n/**\n * Options for {@link BaseConvexClient.subscribe}.\n *\n * @public\n */\nexport interface SubscribeOptions {\n  /**\n   * An (optional) journal produced from a previous execution of this query\n   * function.\n   *\n   * If there is an existing subscription to a query function with the same\n   * name and arguments, this journal will have no effect.\n   */\n  journal?: QueryJournal;\n\n  /**\n   * @internal\n   */\n  componentPath?: string;\n}\n\n/**\n * Options for {@link BaseConvexClient.mutation}.\n *\n * @public\n */\nexport interface MutationOptions {\n  /**\n   * An optimistic update to apply along with this mutation.\n   *\n   * An optimistic update locally updates queries while a mutation is pending.\n   * Once the mutation completes, the update will be rolled back.\n   */\n  optimisticUpdate?: OptimisticUpdate<any>;\n}\n\n/**\n * Type describing updates to a query within a `Transition`.\n *\n * @public\n */\nexport type QueryModification =\n  // `undefined` generally comes from an optimistic update setting the query to be loading\n  { kind: \"Updated\"; result: FunctionResult | undefined } | { kind: \"Removed\" };\n\n/**\n * Object describing a transition passed into the `onTransition` handler.\n *\n * These can be from receiving a transition from the server, or from applying an\n * optimistc update locally.\n *\n * @public\n */\nexport type Transition = {\n  queries: Array<{ token: QueryToken; modification: QueryModification }>;\n  reflectedMutations: Array<{ requestId: RequestId; result: FunctionResult }>;\n  timestamp: TS;\n};\n\n/**\n * Low-level client for directly integrating state management libraries\n * with Convex.\n *\n * Most developers should use higher level clients, like\n * the {@link ConvexHttpClient} or the React hook based {@link react.ConvexReactClient}.\n *\n * @public\n */\nexport class BaseConvexClient {\n  private readonly address: string;\n  private readonly state: LocalSyncState;\n  private readonly requestManager: RequestManager;\n  private readonly webSocketManager: WebSocketManager;\n  private readonly authenticationManager: AuthenticationManager;\n  private remoteQuerySet: RemoteQuerySet;\n  private readonly optimisticQueryResults: OptimisticQueryResults;\n  private _transitionHandlerCounter = 0;\n  private _nextRequestId: RequestId;\n  private _onTransitionFns: Map<number, (transition: Transition) => void> =\n    new Map();\n  private readonly _sessionId: string;\n  private firstMessageReceived = false;\n  private readonly debug: boolean;\n  private readonly logger: Logger;\n  private maxObservedTimestamp: TS | undefined;\n\n  /**\n   * @param address - The url of your Convex deployment, often provided\n   * by an environment variable. E.g. `https://small-mouse-123.convex.cloud`.\n   * @param onTransition - A callback receiving an array of query tokens\n   * corresponding to query results that have changed -- additional handlers\n   * can be added via `addOnTransitionHandler`.\n   * @param options - See {@link BaseConvexClientOptions} for a full description.\n   */\n  constructor(\n    address: string,\n    onTransition: (updatedQueries: QueryToken[]) => void,\n    options?: BaseConvexClientOptions,\n  ) {\n    if (typeof address === \"object\") {\n      throw new Error(\n        \"Passing a ClientConfig object is no longer supported. Pass the URL of the Convex deployment as a string directly.\",\n      );\n    }\n    if (options?.skipConvexDeploymentUrlCheck !== true) {\n      validateDeploymentUrl(address);\n    }\n    options = { ...options };\n    const authRefreshTokenLeewaySeconds =\n      options.authRefreshTokenLeewaySeconds ?? 2;\n    let webSocketConstructor = options.webSocketConstructor;\n    if (!webSocketConstructor && typeof WebSocket === \"undefined\") {\n      throw new Error(\n        \"No WebSocket global variable defined! To use Convex in an environment without WebSocket try the HTTP client: https://docs.convex.dev/api/classes/browser.ConvexHttpClient\",\n      );\n    }\n    webSocketConstructor = webSocketConstructor || WebSocket;\n    this.debug = options.reportDebugInfoToConvex ?? false;\n    this.address = address;\n    this.logger =\n      options.logger === false\n        ? instantiateNoopLogger({ verbose: options.verbose ?? false })\n        : options.logger !== true && options.logger\n          ? options.logger\n          : instantiateDefaultLogger({ verbose: options.verbose ?? false });\n    // Substitute http(s) with ws(s)\n    const i = address.search(\"://\");\n    if (i === -1) {\n      throw new Error(\"Provided address was not an absolute URL.\");\n    }\n    const origin = address.substring(i + 3); // move past the double slash\n    const protocol = address.substring(0, i);\n    let wsProtocol;\n    if (protocol === \"http\") {\n      wsProtocol = \"ws\";\n    } else if (protocol === \"https\") {\n      wsProtocol = \"wss\";\n    } else {\n      throw new Error(`Unknown parent protocol ${protocol}`);\n    }\n    const wsUri = `${wsProtocol}://${origin}/api/${version}/sync`;\n\n    this.state = new LocalSyncState();\n    this.remoteQuerySet = new RemoteQuerySet(\n      (queryId) => this.state.queryPath(queryId),\n      this.logger,\n    );\n    this.requestManager = new RequestManager(this.logger);\n    this.authenticationManager = new AuthenticationManager(\n      this.state,\n      {\n        authenticate: (token) => {\n          const message = this.state.setAuth(token);\n          this.webSocketManager.sendMessage(message);\n          return message.baseVersion;\n        },\n        stopSocket: () => this.webSocketManager.stop(),\n        tryRestartSocket: () => this.webSocketManager.tryRestart(),\n        pauseSocket: () => {\n          this.webSocketManager.pause();\n          this.state.pause();\n        },\n        resumeSocket: () => this.webSocketManager.resume(),\n        clearAuth: () => {\n          this.clearAuth();\n        },\n      },\n      {\n        logger: this.logger,\n        refreshTokenLeewaySeconds: authRefreshTokenLeewaySeconds,\n      },\n    );\n    this.optimisticQueryResults = new OptimisticQueryResults();\n    this.addOnTransitionHandler((transition) => {\n      onTransition(transition.queries.map((q) => q.token));\n    });\n    this._nextRequestId = 0;\n    this._sessionId = newSessionId();\n\n    const { unsavedChangesWarning } = options;\n    if (\n      typeof window === \"undefined\" ||\n      typeof window.addEventListener === \"undefined\"\n    ) {\n      if (unsavedChangesWarning === true) {\n        throw new Error(\n          \"unsavedChangesWarning requested, but window.addEventListener not found! Remove {unsavedChangesWarning: true} from Convex client options.\",\n        );\n      }\n    } else if (unsavedChangesWarning !== false) {\n      // Listen for tab close events and notify the user on unsaved changes.\n      window.addEventListener(\"beforeunload\", (e) => {\n        if (this.requestManager.hasIncompleteRequests()) {\n          // There are 3 different ways to trigger this pop up so just try all of\n          // them.\n\n          e.preventDefault();\n          // This confirmation message doesn't actually appear in most modern\n          // browsers but we tried.\n          const confirmationMessage =\n            \"Are you sure you want to leave? Your changes may not be saved.\";\n          (e || window.event).returnValue = confirmationMessage;\n          return confirmationMessage;\n        }\n      });\n    }\n\n    this.webSocketManager = new WebSocketManager(\n      wsUri,\n      {\n        onOpen: (reconnectMetadata: ReconnectMetadata) => {\n          // We have a new WebSocket!\n          this.mark(\"convexWebSocketOpen\");\n          this.webSocketManager.sendMessage({\n            ...reconnectMetadata,\n            type: \"Connect\",\n            sessionId: this._sessionId,\n            maxObservedTimestamp: this.maxObservedTimestamp,\n          });\n\n          // Throw out our remote query, reissue queries\n          // and outstanding mutations, and reauthenticate.\n          const oldRemoteQueryResults = new Set(\n            this.remoteQuerySet.remoteQueryResults().keys(),\n          );\n          this.remoteQuerySet = new RemoteQuerySet(\n            (queryId) => this.state.queryPath(queryId),\n            this.logger,\n          );\n          const [querySetModification, authModification] = this.state.restart(\n            oldRemoteQueryResults,\n          );\n          if (authModification) {\n            this.webSocketManager.sendMessage(authModification);\n          }\n          this.webSocketManager.sendMessage(querySetModification);\n          for (const message of this.requestManager.restart()) {\n            this.webSocketManager.sendMessage(message);\n          }\n        },\n        onResume: () => {\n          const [querySetModification, authModification] = this.state.resume();\n          if (authModification) {\n            this.webSocketManager.sendMessage(authModification);\n          }\n          if (querySetModification) {\n            this.webSocketManager.sendMessage(querySetModification);\n          }\n          for (const message of this.requestManager.resume()) {\n            this.webSocketManager.sendMessage(message);\n          }\n        },\n        onMessage: (serverMessage: ServerMessage) => {\n          // Metrics events grow linearly with reconnection attempts so this\n          // conditional prevents n^2 metrics reporting.\n          if (!this.firstMessageReceived) {\n            this.firstMessageReceived = true;\n            this.mark(\"convexFirstMessageReceived\");\n            this.reportMarks();\n          }\n          switch (serverMessage.type) {\n            case \"Transition\": {\n              this.observedTimestamp(serverMessage.endVersion.ts);\n              this.authenticationManager.onTransition(serverMessage);\n              this.remoteQuerySet.transition(serverMessage);\n              this.state.transition(serverMessage);\n              const completedRequests = this.requestManager.removeCompleted(\n                this.remoteQuerySet.timestamp(),\n              );\n              this.notifyOnQueryResultChanges(completedRequests);\n              break;\n            }\n            case \"MutationResponse\": {\n              if (serverMessage.success) {\n                this.observedTimestamp(serverMessage.ts);\n              }\n              const completedMutationInfo =\n                this.requestManager.onResponse(serverMessage);\n              if (completedMutationInfo !== null) {\n                this.notifyOnQueryResultChanges(\n                  new Map([\n                    [\n                      completedMutationInfo.requestId,\n                      completedMutationInfo.result,\n                    ],\n                  ]),\n                );\n              }\n              break;\n            }\n            case \"ActionResponse\": {\n              this.requestManager.onResponse(serverMessage);\n              break;\n            }\n            case \"AuthError\": {\n              this.authenticationManager.onAuthError(serverMessage);\n              break;\n            }\n            case \"FatalError\": {\n              const error = logFatalError(this.logger, serverMessage.error);\n              void this.webSocketManager.terminate();\n              throw error;\n            }\n            case \"Ping\":\n              break; // do nothing\n            default: {\n              const _typeCheck: never = serverMessage;\n            }\n          }\n\n          return {\n            hasSyncedPastLastReconnect: this.hasSyncedPastLastReconnect(),\n          };\n        },\n      },\n      webSocketConstructor,\n      this.logger,\n    );\n    this.mark(\"convexClientConstructed\");\n  }\n\n  /**\n   * Return true if there is outstanding work from prior to the time of the most recent restart.\n   * This indicates that the client has not proven itself to have gotten past the issue that\n   * potentially led to the restart. Use this to influence when to reset backoff after a failure.\n   */\n  private hasSyncedPastLastReconnect() {\n    const hasSyncedPastLastReconnect =\n      this.requestManager.hasSyncedPastLastReconnect() ||\n      this.state.hasSyncedPastLastReconnect();\n    return hasSyncedPastLastReconnect;\n  }\n\n  private observedTimestamp(observedTs: TS) {\n    if (\n      this.maxObservedTimestamp === undefined ||\n      this.maxObservedTimestamp.lessThanOrEqual(observedTs)\n    ) {\n      this.maxObservedTimestamp = observedTs;\n    }\n  }\n\n  getMaxObservedTimestamp() {\n    return this.maxObservedTimestamp;\n  }\n\n  /**\n   * Compute the current query results based on the remoteQuerySet and the\n   * current optimistic updates and call `onTransition` for all the changed\n   * queries.\n   *\n   * @param completedMutations - A set of mutation IDs whose optimistic updates\n   * are no longer needed.\n   */\n  private notifyOnQueryResultChanges(\n    completedRequests: Map<RequestId, FunctionResult>,\n  ) {\n    const remoteQueryResults: Map<QueryId, FunctionResult> =\n      this.remoteQuerySet.remoteQueryResults();\n    const queryTokenToValue: QueryResultsMap = new Map();\n    for (const [queryId, result] of remoteQueryResults) {\n      const queryToken = this.state.queryToken(queryId);\n      // It's possible that we've already unsubscribed to this query but\n      // the server hasn't learned about that yet. If so, ignore this one.\n\n      if (queryToken !== null) {\n        const query = {\n          result,\n          udfPath: this.state.queryPath(queryId)!,\n          args: this.state.queryArgs(queryId)!,\n        };\n        queryTokenToValue.set(queryToken, query);\n      }\n    }\n    const changedQueryTokens =\n      this.optimisticQueryResults.ingestQueryResultsFromServer(\n        queryTokenToValue,\n        new Set(completedRequests.keys()),\n      );\n\n    this.handleTransition({\n      queries: changedQueryTokens.map((token) => ({\n        token,\n        modification: {\n          kind: \"Updated\",\n          result: queryTokenToValue.get(token)!.result,\n        },\n      })),\n      reflectedMutations: Array.from(completedRequests).map(\n        ([requestId, result]) => ({\n          requestId,\n          result,\n        }),\n      ),\n      timestamp: this.remoteQuerySet.timestamp(),\n    });\n  }\n\n  private handleTransition(transition: Transition) {\n    for (const fn of this._onTransitionFns.values()) {\n      fn(transition);\n    }\n  }\n\n  /**\n   * Add a handler that will be called on a transition.\n   *\n   * Any external side effects (e.g. setting React state) should be handled here.\n   *\n   * @param fn\n   *\n   * @returns\n   */\n  addOnTransitionHandler(fn: (transition: Transition) => void) {\n    const id = this._transitionHandlerCounter++;\n    this._onTransitionFns.set(id, fn);\n    return () => this._onTransitionFns.delete(id);\n  }\n\n  /**\n   * Set the authentication token to be used for subsequent queries and mutations.\n   * `fetchToken` will be called automatically again if a token expires.\n   * `fetchToken` should return `null` if the token cannot be retrieved, for example\n   * when the user's rights were permanently revoked.\n   * @param fetchToken - an async function returning the JWT-encoded OpenID Connect Identity Token\n   * @param onChange - a callback that will be called when the authentication status changes\n   */\n  setAuth(\n    fetchToken: AuthTokenFetcher,\n    onChange: (isAuthenticated: boolean) => void,\n  ) {\n    void this.authenticationManager.setConfig(fetchToken, onChange);\n  }\n\n  hasAuth() {\n    return this.state.hasAuth();\n  }\n\n  /** @internal */\n  setAdminAuth(value: string, fakeUserIdentity?: UserIdentityAttributes) {\n    const message = this.state.setAdminAuth(value, fakeUserIdentity);\n    this.webSocketManager.sendMessage(message);\n  }\n\n  clearAuth() {\n    const message = this.state.clearAuth();\n    this.webSocketManager.sendMessage(message);\n  }\n\n  /**\n   * Subscribe to a query function.\n   *\n   * Whenever this query's result changes, the `onTransition` callback\n   * passed into the constructor will be called.\n   *\n   * @param name - The name of the query.\n   * @param args - An arguments object for the query. If this is omitted, the\n   * arguments will be `{}`.\n   * @param options - A {@link SubscribeOptions} options object for this query.\n\n   * @returns An object containing a {@link QueryToken} corresponding to this\n   * query and an `unsubscribe` callback.\n   */\n  subscribe(\n    name: string,\n    args?: Record<string, Value>,\n    options?: SubscribeOptions,\n  ): { queryToken: QueryToken; unsubscribe: () => void } {\n    const argsObject = parseArgs(args);\n\n    const { modification, queryToken, unsubscribe } = this.state.subscribe(\n      name,\n      argsObject,\n      options?.journal,\n      options?.componentPath,\n    );\n    if (modification !== null) {\n      this.webSocketManager.sendMessage(modification);\n    }\n    return {\n      queryToken,\n      unsubscribe: () => {\n        const modification = unsubscribe();\n        if (modification) {\n          this.webSocketManager.sendMessage(modification);\n        }\n      },\n    };\n  }\n\n  /**\n   * A query result based only on the current, local state.\n   *\n   * The only way this will return a value is if we're already subscribed to the\n   * query or its value has been set optimistically.\n   */\n  localQueryResult(\n    udfPath: string,\n    args?: Record<string, Value>,\n  ): Value | undefined {\n    const argsObject = parseArgs(args);\n    const queryToken = serializePathAndArgs(udfPath, argsObject);\n    return this.optimisticQueryResults.queryResult(queryToken);\n  }\n\n  /**\n   * Get query result by query token based on current, local state\n   *\n   * The only way this will return a value is if we're already subscribed to the\n   * query or its value has been set optimistically.\n   *\n   * @internal\n   */\n  localQueryResultByToken(queryToken: QueryToken): Value | undefined {\n    return this.optimisticQueryResults.queryResult(queryToken);\n  }\n\n  /**\n   * Whether local query result is available for a toke.\n   *\n   * This method does not throw if the result is an error.\n   *\n   * @internal\n   */\n  hasLocalQueryResultByToken(queryToken: QueryToken): boolean {\n    return this.optimisticQueryResults.hasQueryResult(queryToken);\n  }\n\n  /**\n   * @internal\n   */\n  localQueryLogs(\n    udfPath: string,\n    args?: Record<string, Value>,\n  ): string[] | undefined {\n    const argsObject = parseArgs(args);\n    const queryToken = serializePathAndArgs(udfPath, argsObject);\n    return this.optimisticQueryResults.queryLogs(queryToken);\n  }\n\n  /**\n   * Retrieve the current {@link QueryJournal} for this query function.\n   *\n   * If we have not yet received a result for this query, this will be `undefined`.\n   *\n   * @param name - The name of the query.\n   * @param args - The arguments object for this query.\n   * @returns The query's {@link QueryJournal} or `undefined`.\n   */\n  queryJournal(\n    name: string,\n    args?: Record<string, Value>,\n  ): QueryJournal | undefined {\n    const argsObject = parseArgs(args);\n    const queryToken = serializePathAndArgs(name, argsObject);\n    return this.state.queryJournal(queryToken);\n  }\n\n  /**\n   * Get the current {@link ConnectionState} between the client and the Convex\n   * backend.\n   *\n   * @returns The {@link ConnectionState} with the Convex backend.\n   */\n  connectionState(): ConnectionState {\n    const wsConnectionState = this.webSocketManager.connectionState();\n    return {\n      hasInflightRequests: this.requestManager.hasInflightRequests(),\n      isWebSocketConnected: wsConnectionState.isConnected,\n      hasEverConnected: wsConnectionState.hasEverConnected,\n      connectionCount: wsConnectionState.connectionCount,\n      connectionRetries: wsConnectionState.connectionRetries,\n      timeOfOldestInflightRequest:\n        this.requestManager.timeOfOldestInflightRequest(),\n      inflightMutations: this.requestManager.inflightMutations(),\n      inflightActions: this.requestManager.inflightActions(),\n    };\n  }\n\n  /**\n   * Execute a mutation function.\n   *\n   * @param name - The name of the mutation.\n   * @param args - An arguments object for the mutation. If this is omitted,\n   * the arguments will be `{}`.\n   * @param options - A {@link MutationOptions} options object for this mutation.\n\n   * @returns - A promise of the mutation's result.\n   */\n  async mutation(\n    name: string,\n    args?: Record<string, Value>,\n    options?: MutationOptions,\n  ): Promise<any> {\n    const result = await this.mutationInternal(name, args, options);\n    if (!result.success) {\n      if (result.errorData !== undefined) {\n        throw forwardData(\n          result,\n          new ConvexError(\n            createHybridErrorStacktrace(\"mutation\", name, result),\n          ),\n        );\n      }\n      throw new Error(createHybridErrorStacktrace(\"mutation\", name, result));\n    }\n    return result.value;\n  }\n\n  /**\n   * @internal\n   */\n  async mutationInternal(\n    udfPath: string,\n    args?: Record<string, Value>,\n    options?: MutationOptions,\n    componentPath?: string,\n  ): Promise<FunctionResult> {\n    const { mutationPromise } = this.enqueueMutation(\n      udfPath,\n      args,\n      options,\n      componentPath,\n    );\n    return mutationPromise;\n  }\n\n  /**\n   * @internal\n   */\n  enqueueMutation(\n    udfPath: string,\n    args?: Record<string, Value>,\n    options?: MutationOptions,\n    componentPath?: string,\n  ): { requestId: RequestId; mutationPromise: Promise<FunctionResult> } {\n    const mutationArgs = parseArgs(args);\n    this.tryReportLongDisconnect();\n    const requestId = this.nextRequestId;\n    this._nextRequestId++;\n\n    if (options !== undefined) {\n      const optimisticUpdate = options.optimisticUpdate;\n      if (optimisticUpdate !== undefined) {\n        const wrappedUpdate = (localQueryStore: OptimisticLocalStore) => {\n          const result: unknown = optimisticUpdate(\n            localQueryStore,\n            mutationArgs,\n          );\n          if (result instanceof Promise) {\n            this.logger.warn(\n              \"Optimistic update handler returned a Promise. Optimistic updates should be synchronous.\",\n            );\n          }\n        };\n\n        const changedQueryTokens =\n          this.optimisticQueryResults.applyOptimisticUpdate(\n            wrappedUpdate,\n            requestId,\n          );\n\n        const changedQueries = changedQueryTokens.map((token) => {\n          const localResult = this.localQueryResultByToken(token);\n          return {\n            token,\n            modification: {\n              kind: \"Updated\" as const,\n              result:\n                localResult === undefined\n                  ? undefined\n                  : {\n                      success: true as const,\n                      value: localResult,\n                      logLines: [],\n                    },\n            },\n          };\n        });\n        this.handleTransition({\n          queries: changedQueries,\n          reflectedMutations: [],\n          timestamp: this.remoteQuerySet.timestamp(),\n        });\n      }\n    }\n\n    const message: MutationRequest = {\n      type: \"Mutation\",\n      requestId,\n      udfPath,\n      componentPath,\n      args: [convexToJson(mutationArgs)],\n    };\n    const mightBeSent = this.webSocketManager.sendMessage(message);\n    const mutationPromise = this.requestManager.request(message, mightBeSent);\n    return {\n      requestId,\n      mutationPromise,\n    };\n  }\n\n  /**\n   * Execute an action function.\n   *\n   * @param name - The name of the action.\n   * @param args - An arguments object for the action. If this is omitted,\n   * the arguments will be `{}`.\n   * @returns A promise of the action's result.\n   */\n  async action(name: string, args?: Record<string, Value>): Promise<any> {\n    const result = await this.actionInternal(name, args);\n    if (!result.success) {\n      if (result.errorData !== undefined) {\n        throw forwardData(\n          result,\n          new ConvexError(createHybridErrorStacktrace(\"action\", name, result)),\n        );\n      }\n      throw new Error(createHybridErrorStacktrace(\"action\", name, result));\n    }\n    return result.value;\n  }\n\n  /**\n   * @internal\n   */\n  async actionInternal(\n    udfPath: string,\n    args?: Record<string, Value>,\n    componentPath?: string,\n  ): Promise<FunctionResult> {\n    const actionArgs = parseArgs(args);\n    const requestId = this.nextRequestId;\n    this._nextRequestId++;\n    this.tryReportLongDisconnect();\n\n    const message: ActionRequest = {\n      type: \"Action\",\n      requestId,\n      udfPath,\n      componentPath,\n      args: [convexToJson(actionArgs)],\n    };\n\n    const mightBeSent = this.webSocketManager.sendMessage(message);\n    return this.requestManager.request(message, mightBeSent);\n  }\n\n  /**\n   * Close any network handles associated with this client and stop all subscriptions.\n   *\n   * Call this method when you're done with an {@link BaseConvexClient} to\n   * dispose of its sockets and resources.\n   *\n   * @returns A `Promise` fulfilled when the connection has been completely closed.\n   */\n  async close(): Promise<void> {\n    this.authenticationManager.stop();\n    return this.webSocketManager.terminate();\n  }\n\n  /**\n   * Return the address for this client, useful for creating a new client.\n   *\n   * Not guaranteed to match the address with which this client was constructed:\n   * it may be canonicalized.\n   */\n  get url() {\n    return this.address;\n  }\n\n  /**\n   * @internal\n   */\n  get nextRequestId() {\n    return this._nextRequestId;\n  }\n\n  /**\n   * @internal\n   */\n  get sessionId() {\n    return this._sessionId;\n  }\n\n  // Instance property so that `mark()` doesn't need to be called as a method.\n  private mark = (name: MarkName) => {\n    if (this.debug) {\n      mark(name, this.sessionId);\n    }\n  };\n\n  /**\n   * Reports performance marks to the server. This should only be called when\n   * we have a functional websocket.\n   */\n  private reportMarks() {\n    if (this.debug) {\n      const report = getMarksReport(this.sessionId);\n      this.webSocketManager.sendMessage({\n        type: \"Event\",\n        eventType: \"ClientConnect\",\n        event: report,\n      });\n    }\n  }\n\n  private tryReportLongDisconnect() {\n    if (!this.debug) {\n      return;\n    }\n    const timeOfOldestRequest =\n      this.connectionState().timeOfOldestInflightRequest;\n    if (\n      timeOfOldestRequest === null ||\n      Date.now() - timeOfOldestRequest.getTime() <= 60 * 1000\n    ) {\n      return;\n    }\n    const endpoint = `${this.address}/api/debug_event`;\n    fetch(endpoint, {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n        \"Convex-Client\": `npm-${version}`,\n      },\n      body: JSON.stringify({ event: \"LongWebsocketDisconnect\" }),\n    })\n      .then((response) => {\n        if (!response.ok) {\n          this.logger.warn(\n            \"Analytics request failed with response:\",\n            response.body,\n          );\n        }\n      })\n      .catch((error) => {\n        this.logger.warn(\"Analytics response failed with error:\", error);\n      });\n  }\n}\n"], "mappings": ";;;;AAAA,SAAS,eAAe;AACxB,SAAS,oBAA2B;AACpC;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OAEK;AACP,SAAS,sBAAsB;AAC/B,SAAS,sBAAsB;AAK/B;AAAA,EACE;AAAA,OAEK;AAWP,SAAS,sBAAsB;AAC/B,SAAqB,4BAA4B;AACjD,SAA4B,wBAAwB;AACpD,SAAS,oBAAoB;AAE7B;AAAA,EACE;AAAA,OAEK;AAEP,SAAS,gBAAgB,YAAsB;AAC/C,SAAS,WAAW,6BAA6B;AACjD,SAAS,mBAAmB;AAoKrB,aAAM,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA0B5B,YACE,SACA,cACA,SACA;AA7BF,wBAAiB;AACjB,wBAAiB;AACjB,wBAAiB;AACjB,wBAAiB;AACjB,wBAAiB;AACjB,wBAAQ;AACR,wBAAiB;AACjB,wBAAQ,6BAA4B;AACpC,wBAAQ;AACR,wBAAQ,oBACN,oBAAI,IAAI;AACV,wBAAiB;AACjB,wBAAQ,wBAAuB;AAC/B,wBAAiB;AACjB,wBAAiB;AACjB,wBAAQ;AA+rBR;AAAA,wBAAQ,QAAO,CAAC,SAAmB;AACjC,UAAI,KAAK,OAAO;AACd,aAAK,MAAM,KAAK,SAAS;AAAA,MAC3B;AAAA,IACF;AAprBE,QAAI,OAAO,YAAY,UAAU;AAC/B,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AACA,QAAI,SAAS,iCAAiC,MAAM;AAClD,4BAAsB,OAAO;AAAA,IAC/B;AACA,cAAU,EAAE,GAAG,QAAQ;AACvB,UAAM,gCACJ,QAAQ,iCAAiC;AAC3C,QAAI,uBAAuB,QAAQ;AACnC,QAAI,CAAC,wBAAwB,OAAO,cAAc,aAAa;AAC7D,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AACA,2BAAuB,wBAAwB;AAC/C,SAAK,QAAQ,QAAQ,2BAA2B;AAChD,SAAK,UAAU;AACf,SAAK,SACH,QAAQ,WAAW,QACf,sBAAsB,EAAE,SAAS,QAAQ,WAAW,MAAM,CAAC,IAC3D,QAAQ,WAAW,QAAQ,QAAQ,SACjC,QAAQ,SACR,yBAAyB,EAAE,SAAS,QAAQ,WAAW,MAAM,CAAC;AAEtE,UAAM,IAAI,QAAQ,OAAO,KAAK;AAC9B,QAAI,MAAM,IAAI;AACZ,YAAM,IAAI,MAAM,2CAA2C;AAAA,IAC7D;AACA,UAAM,SAAS,QAAQ,UAAU,IAAI,CAAC;AACtC,UAAM,WAAW,QAAQ,UAAU,GAAG,CAAC;AACvC,QAAI;AACJ,QAAI,aAAa,QAAQ;AACvB,mBAAa;AAAA,IACf,WAAW,aAAa,SAAS;AAC/B,mBAAa;AAAA,IACf,OAAO;AACL,YAAM,IAAI,MAAM,2BAA2B,QAAQ,EAAE;AAAA,IACvD;AACA,UAAM,QAAQ,GAAG,UAAU,MAAM,MAAM,QAAQ,OAAO;AAEtD,SAAK,QAAQ,IAAI,eAAe;AAChC,SAAK,iBAAiB,IAAI;AAAA,MACxB,CAAC,YAAY,KAAK,MAAM,UAAU,OAAO;AAAA,MACzC,KAAK;AAAA,IACP;AACA,SAAK,iBAAiB,IAAI,eAAe,KAAK,MAAM;AACpD,SAAK,wBAAwB,IAAI;AAAA,MAC/B,KAAK;AAAA,MACL;AAAA,QACE,cAAc,CAAC,UAAU;AACvB,gBAAM,UAAU,KAAK,MAAM,QAAQ,KAAK;AACxC,eAAK,iBAAiB,YAAY,OAAO;AACzC,iBAAO,QAAQ;AAAA,QACjB;AAAA,QACA,YAAY,MAAM,KAAK,iBAAiB,KAAK;AAAA,QAC7C,kBAAkB,MAAM,KAAK,iBAAiB,WAAW;AAAA,QACzD,aAAa,MAAM;AACjB,eAAK,iBAAiB,MAAM;AAC5B,eAAK,MAAM,MAAM;AAAA,QACnB;AAAA,QACA,cAAc,MAAM,KAAK,iBAAiB,OAAO;AAAA,QACjD,WAAW,MAAM;AACf,eAAK,UAAU;AAAA,QACjB;AAAA,MACF;AAAA,MACA;AAAA,QACE,QAAQ,KAAK;AAAA,QACb,2BAA2B;AAAA,MAC7B;AAAA,IACF;AACA,SAAK,yBAAyB,IAAI,uBAAuB;AACzD,SAAK,uBAAuB,CAAC,eAAe;AAC1C,mBAAa,WAAW,QAAQ,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC;AAAA,IACrD,CAAC;AACD,SAAK,iBAAiB;AACtB,SAAK,aAAa,aAAa;AAE/B,UAAM,EAAE,sBAAsB,IAAI;AAClC,QACE,OAAO,WAAW,eAClB,OAAO,OAAO,qBAAqB,aACnC;AACA,UAAI,0BAA0B,MAAM;AAClC,cAAM,IAAI;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA,IACF,WAAW,0BAA0B,OAAO;AAE1C,aAAO,iBAAiB,gBAAgB,CAAC,MAAM;AAC7C,YAAI,KAAK,eAAe,sBAAsB,GAAG;AAI/C,YAAE,eAAe;AAGjB,gBAAM,sBACJ;AACF,WAAC,KAAK,OAAO,OAAO,cAAc;AAClC,iBAAO;AAAA,QACT;AAAA,MACF,CAAC;AAAA,IACH;AAEA,SAAK,mBAAmB,IAAI;AAAA,MAC1B;AAAA,MACA;AAAA,QACE,QAAQ,CAAC,sBAAyC;AAEhD,eAAK,KAAK,qBAAqB;AAC/B,eAAK,iBAAiB,YAAY;AAAA,YAChC,GAAG;AAAA,YACH,MAAM;AAAA,YACN,WAAW,KAAK;AAAA,YAChB,sBAAsB,KAAK;AAAA,UAC7B,CAAC;AAID,gBAAM,wBAAwB,IAAI;AAAA,YAChC,KAAK,eAAe,mBAAmB,EAAE,KAAK;AAAA,UAChD;AACA,eAAK,iBAAiB,IAAI;AAAA,YACxB,CAAC,YAAY,KAAK,MAAM,UAAU,OAAO;AAAA,YACzC,KAAK;AAAA,UACP;AACA,gBAAM,CAAC,sBAAsB,gBAAgB,IAAI,KAAK,MAAM;AAAA,YAC1D;AAAA,UACF;AACA,cAAI,kBAAkB;AACpB,iBAAK,iBAAiB,YAAY,gBAAgB;AAAA,UACpD;AACA,eAAK,iBAAiB,YAAY,oBAAoB;AACtD,qBAAW,WAAW,KAAK,eAAe,QAAQ,GAAG;AACnD,iBAAK,iBAAiB,YAAY,OAAO;AAAA,UAC3C;AAAA,QACF;AAAA,QACA,UAAU,MAAM;AACd,gBAAM,CAAC,sBAAsB,gBAAgB,IAAI,KAAK,MAAM,OAAO;AACnE,cAAI,kBAAkB;AACpB,iBAAK,iBAAiB,YAAY,gBAAgB;AAAA,UACpD;AACA,cAAI,sBAAsB;AACxB,iBAAK,iBAAiB,YAAY,oBAAoB;AAAA,UACxD;AACA,qBAAW,WAAW,KAAK,eAAe,OAAO,GAAG;AAClD,iBAAK,iBAAiB,YAAY,OAAO;AAAA,UAC3C;AAAA,QACF;AAAA,QACA,WAAW,CAAC,kBAAiC;AAG3C,cAAI,CAAC,KAAK,sBAAsB;AAC9B,iBAAK,uBAAuB;AAC5B,iBAAK,KAAK,4BAA4B;AACtC,iBAAK,YAAY;AAAA,UACnB;AACA,kBAAQ,cAAc,MAAM;AAAA,YAC1B,KAAK,cAAc;AACjB,mBAAK,kBAAkB,cAAc,WAAW,EAAE;AAClD,mBAAK,sBAAsB,aAAa,aAAa;AACrD,mBAAK,eAAe,WAAW,aAAa;AAC5C,mBAAK,MAAM,WAAW,aAAa;AACnC,oBAAM,oBAAoB,KAAK,eAAe;AAAA,gBAC5C,KAAK,eAAe,UAAU;AAAA,cAChC;AACA,mBAAK,2BAA2B,iBAAiB;AACjD;AAAA,YACF;AAAA,YACA,KAAK,oBAAoB;AACvB,kBAAI,cAAc,SAAS;AACzB,qBAAK,kBAAkB,cAAc,EAAE;AAAA,cACzC;AACA,oBAAM,wBACJ,KAAK,eAAe,WAAW,aAAa;AAC9C,kBAAI,0BAA0B,MAAM;AAClC,qBAAK;AAAA,kBACH,oBAAI,IAAI;AAAA,oBACN;AAAA,sBACE,sBAAsB;AAAA,sBACtB,sBAAsB;AAAA,oBACxB;AAAA,kBACF,CAAC;AAAA,gBACH;AAAA,cACF;AACA;AAAA,YACF;AAAA,YACA,KAAK,kBAAkB;AACrB,mBAAK,eAAe,WAAW,aAAa;AAC5C;AAAA,YACF;AAAA,YACA,KAAK,aAAa;AAChB,mBAAK,sBAAsB,YAAY,aAAa;AACpD;AAAA,YACF;AAAA,YACA,KAAK,cAAc;AACjB,oBAAM,QAAQ,cAAc,KAAK,QAAQ,cAAc,KAAK;AAC5D,mBAAK,KAAK,iBAAiB,UAAU;AACrC,oBAAM;AAAA,YACR;AAAA,YACA,KAAK;AACH;AAAA;AAAA,YACF,SAAS;AACP,oBAAM,aAAoB;AAAA,YAC5B;AAAA,UACF;AAEA,iBAAO;AAAA,YACL,4BAA4B,KAAK,2BAA2B;AAAA,UAC9D;AAAA,QACF;AAAA,MACF;AAAA,MACA;AAAA,MACA,KAAK;AAAA,IACP;AACA,SAAK,KAAK,yBAAyB;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOQ,6BAA6B;AACnC,UAAM,6BACJ,KAAK,eAAe,2BAA2B,KAC/C,KAAK,MAAM,2BAA2B;AACxC,WAAO;AAAA,EACT;AAAA,EAEQ,kBAAkB,YAAgB;AACxC,QACE,KAAK,yBAAyB,UAC9B,KAAK,qBAAqB,gBAAgB,UAAU,GACpD;AACA,WAAK,uBAAuB;AAAA,IAC9B;AAAA,EACF;AAAA,EAEA,0BAA0B;AACxB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUQ,2BACN,mBACA;AACA,UAAM,qBACJ,KAAK,eAAe,mBAAmB;AACzC,UAAM,oBAAqC,oBAAI,IAAI;AACnD,eAAW,CAAC,SAAS,MAAM,KAAK,oBAAoB;AAClD,YAAM,aAAa,KAAK,MAAM,WAAW,OAAO;AAIhD,UAAI,eAAe,MAAM;AACvB,cAAM,QAAQ;AAAA,UACZ;AAAA,UACA,SAAS,KAAK,MAAM,UAAU,OAAO;AAAA,UACrC,MAAM,KAAK,MAAM,UAAU,OAAO;AAAA,QACpC;AACA,0BAAkB,IAAI,YAAY,KAAK;AAAA,MACzC;AAAA,IACF;AACA,UAAM,qBACJ,KAAK,uBAAuB;AAAA,MAC1B;AAAA,MACA,IAAI,IAAI,kBAAkB,KAAK,CAAC;AAAA,IAClC;AAEF,SAAK,iBAAiB;AAAA,MACpB,SAAS,mBAAmB,IAAI,CAAC,WAAW;AAAA,QAC1C;AAAA,QACA,cAAc;AAAA,UACZ,MAAM;AAAA,UACN,QAAQ,kBAAkB,IAAI,KAAK,EAAG;AAAA,QACxC;AAAA,MACF,EAAE;AAAA,MACF,oBAAoB,MAAM,KAAK,iBAAiB,EAAE;AAAA,QAChD,CAAC,CAAC,WAAW,MAAM,OAAO;AAAA,UACxB;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,MACA,WAAW,KAAK,eAAe,UAAU;AAAA,IAC3C,CAAC;AAAA,EACH;AAAA,EAEQ,iBAAiB,YAAwB;AAC/C,eAAW,MAAM,KAAK,iBAAiB,OAAO,GAAG;AAC/C,SAAG,UAAU;AAAA,IACf;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,uBAAuB,IAAsC;AAC3D,UAAM,KAAK,KAAK;AAChB,SAAK,iBAAiB,IAAI,IAAI,EAAE;AAChC,WAAO,MAAM,KAAK,iBAAiB,OAAO,EAAE;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,QACE,YACA,UACA;AACA,SAAK,KAAK,sBAAsB,UAAU,YAAY,QAAQ;AAAA,EAChE;AAAA,EAEA,UAAU;AACR,WAAO,KAAK,MAAM,QAAQ;AAAA,EAC5B;AAAA;AAAA,EAGA,aAAa,OAAe,kBAA2C;AACrE,UAAM,UAAU,KAAK,MAAM,aAAa,OAAO,gBAAgB;AAC/D,SAAK,iBAAiB,YAAY,OAAO;AAAA,EAC3C;AAAA,EAEA,YAAY;AACV,UAAM,UAAU,KAAK,MAAM,UAAU;AACrC,SAAK,iBAAiB,YAAY,OAAO;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,UACE,MACA,MACA,SACqD;AACrD,UAAM,aAAa,UAAU,IAAI;AAEjC,UAAM,EAAE,cAAc,YAAY,YAAY,IAAI,KAAK,MAAM;AAAA,MAC3D;AAAA,MACA;AAAA,MACA,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AACA,QAAI,iBAAiB,MAAM;AACzB,WAAK,iBAAiB,YAAY,YAAY;AAAA,IAChD;AACA,WAAO;AAAA,MACL;AAAA,MACA,aAAa,MAAM;AACjB,cAAMA,gBAAe,YAAY;AACjC,YAAIA,eAAc;AAChB,eAAK,iBAAiB,YAAYA,aAAY;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iBACE,SACA,MACmB;AACnB,UAAM,aAAa,UAAU,IAAI;AACjC,UAAM,aAAa,qBAAqB,SAAS,UAAU;AAC3D,WAAO,KAAK,uBAAuB,YAAY,UAAU;AAAA,EAC3D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,wBAAwB,YAA2C;AACjE,WAAO,KAAK,uBAAuB,YAAY,UAAU;AAAA,EAC3D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,2BAA2B,YAAiC;AAC1D,WAAO,KAAK,uBAAuB,eAAe,UAAU;AAAA,EAC9D;AAAA;AAAA;AAAA;AAAA,EAKA,eACE,SACA,MACsB;AACtB,UAAM,aAAa,UAAU,IAAI;AACjC,UAAM,aAAa,qBAAqB,SAAS,UAAU;AAC3D,WAAO,KAAK,uBAAuB,UAAU,UAAU;AAAA,EACzD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,aACE,MACA,MAC0B;AAC1B,UAAM,aAAa,UAAU,IAAI;AACjC,UAAM,aAAa,qBAAqB,MAAM,UAAU;AACxD,WAAO,KAAK,MAAM,aAAa,UAAU;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,kBAAmC;AACjC,UAAM,oBAAoB,KAAK,iBAAiB,gBAAgB;AAChE,WAAO;AAAA,MACL,qBAAqB,KAAK,eAAe,oBAAoB;AAAA,MAC7D,sBAAsB,kBAAkB;AAAA,MACxC,kBAAkB,kBAAkB;AAAA,MACpC,iBAAiB,kBAAkB;AAAA,MACnC,mBAAmB,kBAAkB;AAAA,MACrC,6BACE,KAAK,eAAe,4BAA4B;AAAA,MAClD,mBAAmB,KAAK,eAAe,kBAAkB;AAAA,MACzD,iBAAiB,KAAK,eAAe,gBAAgB;AAAA,IACvD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,SACJ,MACA,MACA,SACc;AACd,UAAM,SAAS,MAAM,KAAK,iBAAiB,MAAM,MAAM,OAAO;AAC9D,QAAI,CAAC,OAAO,SAAS;AACnB,UAAI,OAAO,cAAc,QAAW;AAClC,cAAM;AAAA,UACJ;AAAA,UACA,IAAI;AAAA,YACF,4BAA4B,YAAY,MAAM,MAAM;AAAA,UACtD;AAAA,QACF;AAAA,MACF;AACA,YAAM,IAAI,MAAM,4BAA4B,YAAY,MAAM,MAAM,CAAC;AAAA,IACvE;AACA,WAAO,OAAO;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,iBACJ,SACA,MACA,SACA,eACyB;AACzB,UAAM,EAAE,gBAAgB,IAAI,KAAK;AAAA,MAC/B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,gBACE,SACA,MACA,SACA,eACoE;AACpE,UAAM,eAAe,UAAU,IAAI;AACnC,SAAK,wBAAwB;AAC7B,UAAM,YAAY,KAAK;AACvB,SAAK;AAEL,QAAI,YAAY,QAAW;AACzB,YAAM,mBAAmB,QAAQ;AACjC,UAAI,qBAAqB,QAAW;AAClC,cAAM,gBAAgB,CAAC,oBAA0C;AAC/D,gBAAM,SAAkB;AAAA,YACtB;AAAA,YACA;AAAA,UACF;AACA,cAAI,kBAAkB,SAAS;AAC7B,iBAAK,OAAO;AAAA,cACV;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAEA,cAAM,qBACJ,KAAK,uBAAuB;AAAA,UAC1B;AAAA,UACA;AAAA,QACF;AAEF,cAAM,iBAAiB,mBAAmB,IAAI,CAAC,UAAU;AACvD,gBAAM,cAAc,KAAK,wBAAwB,KAAK;AACtD,iBAAO;AAAA,YACL;AAAA,YACA,cAAc;AAAA,cACZ,MAAM;AAAA,cACN,QACE,gBAAgB,SACZ,SACA;AAAA,gBACE,SAAS;AAAA,gBACT,OAAO;AAAA,gBACP,UAAU,CAAC;AAAA,cACb;AAAA,YACR;AAAA,UACF;AAAA,QACF,CAAC;AACD,aAAK,iBAAiB;AAAA,UACpB,SAAS;AAAA,UACT,oBAAoB,CAAC;AAAA,UACrB,WAAW,KAAK,eAAe,UAAU;AAAA,QAC3C,CAAC;AAAA,MACH;AAAA,IACF;AAEA,UAAM,UAA2B;AAAA,MAC/B,MAAM;AAAA,MACN;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM,CAAC,aAAa,YAAY,CAAC;AAAA,IACnC;AACA,UAAM,cAAc,KAAK,iBAAiB,YAAY,OAAO;AAC7D,UAAM,kBAAkB,KAAK,eAAe,QAAQ,SAAS,WAAW;AACxE,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,OAAO,MAAc,MAA4C;AACrE,UAAM,SAAS,MAAM,KAAK,eAAe,MAAM,IAAI;AACnD,QAAI,CAAC,OAAO,SAAS;AACnB,UAAI,OAAO,cAAc,QAAW;AAClC,cAAM;AAAA,UACJ;AAAA,UACA,IAAI,YAAY,4BAA4B,UAAU,MAAM,MAAM,CAAC;AAAA,QACrE;AAAA,MACF;AACA,YAAM,IAAI,MAAM,4BAA4B,UAAU,MAAM,MAAM,CAAC;AAAA,IACrE;AACA,WAAO,OAAO;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,eACJ,SACA,MACA,eACyB;AACzB,UAAM,aAAa,UAAU,IAAI;AACjC,UAAM,YAAY,KAAK;AACvB,SAAK;AACL,SAAK,wBAAwB;AAE7B,UAAM,UAAyB;AAAA,MAC7B,MAAM;AAAA,MACN;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM,CAAC,aAAa,UAAU,CAAC;AAAA,IACjC;AAEA,UAAM,cAAc,KAAK,iBAAiB,YAAY,OAAO;AAC7D,WAAO,KAAK,eAAe,QAAQ,SAAS,WAAW;AAAA,EACzD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,QAAuB;AAC3B,SAAK,sBAAsB,KAAK;AAChC,WAAO,KAAK,iBAAiB,UAAU;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,MAAM;AACR,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,gBAAgB;AAClB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,YAAY;AACd,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAaQ,cAAc;AACpB,QAAI,KAAK,OAAO;AACd,YAAM,SAAS,eAAe,KAAK,SAAS;AAC5C,WAAK,iBAAiB,YAAY;AAAA,QAChC,MAAM;AAAA,QACN,WAAW;AAAA,QACX,OAAO;AAAA,MACT,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EAEQ,0BAA0B;AAChC,QAAI,CAAC,KAAK,OAAO;AACf;AAAA,IACF;AACA,UAAM,sBACJ,KAAK,gBAAgB,EAAE;AACzB,QACE,wBAAwB,QACxB,KAAK,IAAI,IAAI,oBAAoB,QAAQ,KAAK,KAAK,KACnD;AACA;AAAA,IACF;AACA,UAAM,WAAW,GAAG,KAAK,OAAO;AAChC,UAAM,UAAU;AAAA,MACd,QAAQ;AAAA,MACR,SAAS;AAAA,QACP,gBAAgB;AAAA,QAChB,iBAAiB,OAAO,OAAO;AAAA,MACjC;AAAA,MACA,MAAM,KAAK,UAAU,EAAE,OAAO,0BAA0B,CAAC;AAAA,IAC3D,CAAC,EACE,KAAK,CAAC,aAAa;AAClB,UAAI,CAAC,SAAS,IAAI;AAChB,aAAK,OAAO;AAAA,UACV;AAAA,UACA,SAAS;AAAA,QACX;AAAA,MACF;AAAA,IACF,CAAC,EACA,MAAM,CAAC,UAAU;AAChB,WAAK,OAAO,KAAK,yCAAyC,KAAK;AAAA,IACjE,CAAC;AAAA,EACL;AACF;", "names": ["modification"]}
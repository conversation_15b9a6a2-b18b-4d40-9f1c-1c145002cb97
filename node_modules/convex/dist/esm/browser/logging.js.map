{"version": 3, "sources": ["../../../src/browser/logging.ts"], "sourcesContent": ["/* eslint-disable no-console */ // This is the one file where we can `console.log` for the default logger implementation.\nimport { ConvexError, Value } from \"../values/index.js\";\nimport { FunctionFailure } from \"./sync/function_result.js\";\n\n// This is blue #9 from https://www.radix-ui.com/docs/colors/palette-composition/the-scales\n// It must look good in both light and dark mode.\nconst INFO_COLOR = \"color:rgb(0, 145, 255)\";\n\nexport type UdfType = \"query\" | \"mutation\" | \"action\" | \"any\";\n\nfunction prefix_for_source(source: UdfType) {\n  switch (source) {\n    case \"query\":\n      return \"Q\";\n    case \"mutation\":\n      return \"M\";\n    case \"action\":\n      return \"A\";\n    case \"any\":\n      return \"?\";\n  }\n}\n\nexport type LogLevel = \"debug\" | \"info\" | \"warn\" | \"error\";\n\n/**\n * A logger that can be used to log messages. By default, this is a wrapper\n * around `console`, but can be configured to not log at all or to log somewhere\n * else.\n */\nexport class Logger {\n  private _onLogLineFuncs: Record<\n    string,\n    (level: LogLevel, ...args: any[]) => void\n  >;\n  private _verbose: boolean;\n\n  constructor(options: { verbose: boolean }) {\n    this._onLogLineFuncs = {};\n    this._verbose = options.verbose;\n  }\n\n  addLogLineListener(\n    func: (level: LogLevel, ...args: any[]) => void,\n  ): () => void {\n    let id = Math.random().toString(36).substring(2, 15);\n    for (let i = 0; i < 10; i++) {\n      if (this._onLogLineFuncs[id] === undefined) {\n        break;\n      }\n      id = Math.random().toString(36).substring(2, 15);\n    }\n    this._onLogLineFuncs[id] = func;\n    return () => {\n      delete this._onLogLineFuncs[id];\n    };\n  }\n\n  logVerbose(...args: any[]) {\n    if (this._verbose) {\n      for (const func of Object.values(this._onLogLineFuncs)) {\n        func(\"debug\", `${new Date().toISOString()}`, ...args);\n      }\n    }\n  }\n\n  log(...args: any[]) {\n    for (const func of Object.values(this._onLogLineFuncs)) {\n      func(\"info\", ...args);\n    }\n  }\n\n  warn(...args: any[]) {\n    for (const func of Object.values(this._onLogLineFuncs)) {\n      func(\"warn\", ...args);\n    }\n  }\n\n  error(...args: any[]) {\n    for (const func of Object.values(this._onLogLineFuncs)) {\n      func(\"error\", ...args);\n    }\n  }\n}\n\nexport function instantiateDefaultLogger(options: {\n  verbose: boolean;\n}): Logger {\n  const logger = new Logger(options);\n  logger.addLogLineListener((level, ...args) => {\n    switch (level) {\n      case \"debug\":\n        console.debug(...args);\n        break;\n      case \"info\":\n        console.log(...args);\n        break;\n      case \"warn\":\n        console.warn(...args);\n        break;\n      case \"error\":\n        console.error(...args);\n        break;\n      default: {\n        const _typecheck: never = level;\n        console.log(...args);\n      }\n    }\n  });\n  return logger;\n}\n\nexport function instantiateNoopLogger(options: { verbose: boolean }): Logger {\n  return new Logger(options);\n}\n\nexport function logForFunction(\n  logger: Logger,\n  type: \"info\" | \"error\",\n  source: UdfType,\n  udfPath: string,\n  message: string | { errorData: Value },\n) {\n  const prefix = prefix_for_source(source);\n\n  if (typeof message === \"object\") {\n    message = `ConvexError ${JSON.stringify(message.errorData, null, 2)}`;\n  }\n  if (type === \"info\") {\n    const match = message.match(/^\\[.*?\\] /);\n    if (match === null) {\n      logger.error(\n        `[CONVEX ${prefix}(${udfPath})] Could not parse console.log`,\n      );\n      return;\n    }\n    const level = message.slice(1, match[0].length - 2);\n    const args = message.slice(match[0].length);\n\n    logger.log(`%c[CONVEX ${prefix}(${udfPath})] [${level}]`, INFO_COLOR, args);\n  } else {\n    logger.error(`[CONVEX ${prefix}(${udfPath})] ${message}`);\n  }\n}\n\nexport function logFatalError(logger: Logger, message: string): Error {\n  const errorMessage = `[CONVEX FATAL ERROR] ${message}`;\n  logger.error(errorMessage);\n  return new Error(errorMessage);\n}\n\nexport function createHybridErrorStacktrace(\n  source: UdfType,\n  udfPath: string,\n  result: FunctionFailure,\n): string {\n  const prefix = prefix_for_source(source);\n  return `[CONVEX ${prefix}(${udfPath})] ${result.errorMessage}\\n  Called by client`;\n}\n\nexport function forwardData(\n  result: FunctionFailure,\n  error: ConvexError<string>,\n) {\n  (error as ConvexError<any>).data = result.errorData;\n  return error;\n}\n"], "mappings": ";;;;AAMA,MAAM,aAAa;AAInB,SAAS,kBAAkB,QAAiB;AAC1C,UAAQ,QAAQ;AAAA,IACd,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,EACX;AACF;AASO,aAAM,OAAO;AAAA,EAOlB,YAAY,SAA+B;AAN3C,wBAAQ;AAIR,wBAAQ;AAGN,SAAK,kBAAkB,CAAC;AACxB,SAAK,WAAW,QAAQ;AAAA,EAC1B;AAAA,EAEA,mBACE,MACY;AACZ,QAAI,KAAK,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,UAAU,GAAG,EAAE;AACnD,aAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC3B,UAAI,KAAK,gBAAgB,EAAE,MAAM,QAAW;AAC1C;AAAA,MACF;AACA,WAAK,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,UAAU,GAAG,EAAE;AAAA,IACjD;AACA,SAAK,gBAAgB,EAAE,IAAI;AAC3B,WAAO,MAAM;AACX,aAAO,KAAK,gBAAgB,EAAE;AAAA,IAChC;AAAA,EACF;AAAA,EAEA,cAAc,MAAa;AACzB,QAAI,KAAK,UAAU;AACjB,iBAAW,QAAQ,OAAO,OAAO,KAAK,eAAe,GAAG;AACtD,aAAK,SAAS,IAAG,oBAAI,KAAK,GAAE,YAAY,CAAC,IAAI,GAAG,IAAI;AAAA,MACtD;AAAA,IACF;AAAA,EACF;AAAA,EAEA,OAAO,MAAa;AAClB,eAAW,QAAQ,OAAO,OAAO,KAAK,eAAe,GAAG;AACtD,WAAK,QAAQ,GAAG,IAAI;AAAA,IACtB;AAAA,EACF;AAAA,EAEA,QAAQ,MAAa;AACnB,eAAW,QAAQ,OAAO,OAAO,KAAK,eAAe,GAAG;AACtD,WAAK,QAAQ,GAAG,IAAI;AAAA,IACtB;AAAA,EACF;AAAA,EAEA,SAAS,MAAa;AACpB,eAAW,QAAQ,OAAO,OAAO,KAAK,eAAe,GAAG;AACtD,WAAK,SAAS,GAAG,IAAI;AAAA,IACvB;AAAA,EACF;AACF;AAEO,gBAAS,yBAAyB,SAE9B;AACT,QAAM,SAAS,IAAI,OAAO,OAAO;AACjC,SAAO,mBAAmB,CAAC,UAAU,SAAS;AAC5C,YAAQ,OAAO;AAAA,MACb,KAAK;AACH,gBAAQ,MAAM,GAAG,IAAI;AACrB;AAAA,MACF,KAAK;AACH,gBAAQ,IAAI,GAAG,IAAI;AACnB;AAAA,MACF,KAAK;AACH,gBAAQ,KAAK,GAAG,IAAI;AACpB;AAAA,MACF,KAAK;AACH,gBAAQ,MAAM,GAAG,IAAI;AACrB;AAAA,MACF,SAAS;AACP,cAAM,aAAoB;AAC1B,gBAAQ,IAAI,GAAG,IAAI;AAAA,MACrB;AAAA,IACF;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAEO,gBAAS,sBAAsB,SAAuC;AAC3E,SAAO,IAAI,OAAO,OAAO;AAC3B;AAEO,gBAAS,eACd,QACA,MACA,QACA,SACA,SACA;AACA,QAAM,SAAS,kBAAkB,MAAM;AAEvC,MAAI,OAAO,YAAY,UAAU;AAC/B,cAAU,eAAe,KAAK,UAAU,QAAQ,WAAW,MAAM,CAAC,CAAC;AAAA,EACrE;AACA,MAAI,SAAS,QAAQ;AACnB,UAAM,QAAQ,QAAQ,MAAM,WAAW;AACvC,QAAI,UAAU,MAAM;AAClB,aAAO;AAAA,QACL,WAAW,MAAM,IAAI,OAAO;AAAA,MAC9B;AACA;AAAA,IACF;AACA,UAAM,QAAQ,QAAQ,MAAM,GAAG,MAAM,CAAC,EAAE,SAAS,CAAC;AAClD,UAAM,OAAO,QAAQ,MAAM,MAAM,CAAC,EAAE,MAAM;AAE1C,WAAO,IAAI,aAAa,MAAM,IAAI,OAAO,OAAO,KAAK,KAAK,YAAY,IAAI;AAAA,EAC5E,OAAO;AACL,WAAO,MAAM,WAAW,MAAM,IAAI,OAAO,MAAM,OAAO,EAAE;AAAA,EAC1D;AACF;AAEO,gBAAS,cAAc,QAAgB,SAAwB;AACpE,QAAM,eAAe,wBAAwB,OAAO;AACpD,SAAO,MAAM,YAAY;AACzB,SAAO,IAAI,MAAM,YAAY;AAC/B;AAEO,gBAAS,4BACd,QACA,SACA,QACQ;AACR,QAAM,SAAS,kBAAkB,MAAM;AACvC,SAAO,WAAW,MAAM,IAAI,OAAO,MAAM,OAAO,YAAY;AAAA;AAC9D;AAEO,gBAAS,YACd,QACA,OACA;AACA,EAAC,MAA2B,OAAO,OAAO;AAC1C,SAAO;AACT;", "names": []}
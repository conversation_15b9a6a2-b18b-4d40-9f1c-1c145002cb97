{"version": 3, "file": "api.d.ts", "sourceRoot": "", "sources": ["../../../../src/cli/lib/api.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAA0B,MAAM,0BAA0B,CAAC;AAY3E,OAAO,EAAE,CAAC,EAAE,MAAM,KAAK,CAAC;AACxB,OAAO,EACL,mBAAmB,EACnB,gBAAgB,EACjB,MAAM,0BAA0B,CAAC;AAGlC,MAAM,MAAM,cAAc,GAAG,MAAM,CAAC;AACpC,MAAM,MAAM,mBAAmB,GAAG,MAAM,GAAG,KAAK,GAAG,SAAS,CAAC;AAC7D,MAAM,MAAM,6BAA6B,GAAG,mBAAmB,GAAG,OAAO,CAAC;AAC1E,MAAM,MAAM,cAAc,GAAG,6BAA6B,GAAG,WAAW,CAAC;AAEzE,MAAM,MAAM,OAAO,GAAG;IACpB,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,MAAM,CAAC;IACb,IAAI,EAAE,MAAM,CAAC;IACb,MAAM,EAAE,OAAO,CAAC;CACjB,CAAC;AAEF,KAAK,QAAQ,GAAG,MAAM,CAAC;AAGvB,wBAAsB,aAAa,CACjC,GAAG,EAAE,OAAO,EACZ,EACE,QAAQ,EAAE,gBAAgB,EAC1B,WAAW,EACX,WAAW,EACX,yBAAyB,GAC1B,EAAE;IACD,QAAQ,EAAE,MAAM,CAAC;IACjB,WAAW,EAAE,MAAM,CAAC;IACpB,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,yBAAyB,EAAE,MAAM,GAAG,KAAK,CAAC;CAC3C,GACA,OAAO,CAAC;IACT,WAAW,EAAE,MAAM,CAAC;IACpB,QAAQ,EAAE,MAAM,CAAC;IACjB,iBAAiB,EAAE,MAAM,CAAC;CAC3B,CAAC,CAmCD;AAMD,eAAO,MAAM,sCAAsC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAYlD,CAAC;AAEF,MAAM,MAAM,gCAAgC,GAAG,CAAC,CAAC,KAAK,CACpD,OAAO,sCAAsC,CAC9C,CAAC;AAEF,KAAK,uCAAuC,GAAG;IAC7C,IAAI,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;IAG3B,YAAY,CAAC,EAAE,OAAO,CAAC;IAEvB,WAAW,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IACjC,cAAc,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IACpC,WAAW,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;CAClC,CAAC;AAEF,MAAM,MAAM,0BAA0B,GACpC,uCAAuC,GAAG;IACxC,GAAG,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IACzB,QAAQ,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAC9B,OAAO,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;CAC9B,CAAC;AAEJ,wBAAsB,2CAA2C,CAC/D,GAAG,EAAE,OAAO,EACZ,OAAO,EAAE,0BAA0B,GAClC,OAAO,CAAC,gCAAgC,CAAC,CAiB3C;AAED,wBAAsB,gDAAgD,CACpE,GAAG,EAAE,OAAO,EACZ,mBAAmB,EAAE,gCAAgC,EACrD,MAAM,EAAE,YAAY,GAAG,WAAW,GAAG,SAAS,sBA8B/C;AA6BD,wBAAsB,4BAA4B,CAChD,GAAG,EAAE,OAAO,EACZ,gBAAgB,EAAE,gBAAgB,GACjC,OAAO,CACN;IAAE,IAAI,EAAE,WAAW,CAAC;IAAC,QAAQ,EAAE,MAAM,CAAC;IAAC,WAAW,EAAE,MAAM,CAAA;CAAE,GAC5D;IAAE,IAAI,EAAE,UAAU,CAAA;CAAE,GACpB;IAAE,IAAI,EAAE,SAAS,CAAA;CAAE,CACtB,CA0CA;AA8BD,wBAAsB,0DAA0D,CAC9E,GAAG,EAAE,OAAO,EACZ,gBAAgB,EACZ;IAAE,IAAI,EAAE,qBAAqB,CAAC;IAAC,QAAQ,EAAE,MAAM,CAAC;IAAC,WAAW,EAAE,MAAM,CAAA;CAAE,GACtE;IAAE,IAAI,EAAE,kBAAkB,CAAC;IAAC,gBAAgB,EAAE,MAAM,CAAA;CAAE,EAC1D,cAAc,EAAE,MAAM,GAAG,KAAK,EAC9B,WAAW,EAAE,MAAM,GAAG,SAAS,GAC9B,OAAO,CAAC;IACT,cAAc,EAAE,MAAM,CAAC;IACvB,aAAa,EAAE,MAAM,CAAC;IACtB,QAAQ,EAAE,QAAQ,CAAC;CACpB,CAAC,CA2DD;AA2SD,wBAAsB,iCAAiC,CACrD,GAAG,EAAE,OAAO,EACZ,mBAAmB,EAAE,mBAAmB,EACxC,sBAAsB,EAAE,gCAAgC,EACxD,EAAE,kBAAkB,EAAE;;CAA+B,GACpD,OAAO,CAAC;IACT,QAAQ,EAAE,MAAM,CAAC;IACjB,GAAG,EAAE,MAAM,CAAC;IACZ,gBAAgB,EAAE;QAChB,cAAc,EAAE,MAAM,CAAC;QACvB,cAAc,EAAE,cAAc,CAAC;QAC/B,WAAW,EAAE,MAAM,GAAG,IAAI,CAAC;QAC3B,QAAQ,EAAE,MAAM,GAAG,IAAI,CAAC;KACzB,GAAG,IAAI,CAAC;CACV,CAAC,CA0FD;AAED,wBAAsB,mBAAmB,CACvC,GAAG,EAAE,OAAO,EACZ,cAAc,EAAE,MAAM;UAOd,MAAM;aACH,MAAM;YACP,MAAM;eACH,MAAM;GAgBpB;AAED,wBAAsB,yBAAyB,CAC7C,GAAG,EAAE,OAAO,EAEZ,SAAS,EAAE,MAAM;UAUT,MAAM;aACH,MAAM;YACP,MAAM;eACH,MAAM;GAgBpB"}
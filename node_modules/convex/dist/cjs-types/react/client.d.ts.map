{"version": 3, "file": "client.d.ts", "sourceRoot": "", "sources": ["../../../src/react/client.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,EAAE,gBAAgB,EAAc,MAAM,qBAAqB,CAAC;AACxE,OAAO,KAA8B,MAAM,OAAO,CAAC;AACnD,OAAO,EAAgB,KAAK,EAAE,MAAM,oBAAoB,CAAC;AACzD,OAAO,EAAE,YAAY,EAAE,MAAM,6BAA6B,CAAC;AAC3D,OAAO,EACL,gBAAgB,EAChB,uBAAuB,EACvB,eAAe,EAChB,MAAM,2BAA2B,CAAC;AAInC,OAAO,EACL,cAAc,EACd,YAAY,EACZ,iBAAiB,EACjB,kBAAkB,EAClB,gBAAgB,EAGjB,MAAM,kBAAkB,CAAC;AAC1B,OAAO,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;AACxD,OAAO,EAGL,MAAM,EACP,MAAM,uBAAuB,CAAC;AAQ/B;;;;GAIG;AACH,MAAM,WAAW,aAAa,CAAC,QAAQ,SAAS,iBAAiB,CAAC,UAAU,CAAC;IAC3E;;;;;OAKG;IACH,CAAC,GAAG,IAAI,EAAE,gBAAgB,CAAC,QAAQ,CAAC,GAAG,OAAO,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC;IAE7E;;;;;;;;;;;;;;;;;;;;OAoBG;IACH,oBAAoB,CAClB,gBAAgB,EAAE,gBAAgB,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,GACzD,aAAa,CAAC,QAAQ,CAAC,CAAC;CAC5B;AAGD,wBAAgB,cAAc,CAC5B,iBAAiB,EAAE,iBAAiB,CAAC,UAAU,CAAC,EAChD,MAAM,EAAE,iBAAiB,EACzB,MAAM,CAAC,EAAE,gBAAgB,CAAC,GAAG,CAAC,GAC7B,aAAa,CAAC,GAAG,CAAC,CAqBpB;AAED;;;;GAIG;AACH,MAAM,WAAW,WAAW,CAAC,MAAM,SAAS,iBAAiB,CAAC,QAAQ,CAAC;IACrE;;;;;;OAMG;IACH,CAAC,GAAG,IAAI,EAAE,gBAAgB,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC;CAC1E;AAWD;;;;GAIG;AACH,MAAM,WAAW,KAAK,CAAC,CAAC;IACtB;;;;;;;;;;;;;OAaG;IACH,QAAQ,CAAC,QAAQ,EAAE,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC;IAE3C;;;;;;;;;OASG;IACH,gBAAgB,IAAI,CAAC,GAAG,SAAS,CAAC;IAOlC;;;;OAIG;IACH,OAAO,IAAI,YAAY,GAAG,SAAS,CAAC;CACrC;AAED;;;;GAIG;AACH,MAAM,WAAW,iBAAiB;IAChC;;;;;;OAMG;IACH,OAAO,CAAC,EAAE,YAAY,CAAC;CAMxB;AAED;;;;GAIG;AACH,MAAM,WAAW,eAAe,CAAC,IAAI,SAAS,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC;IACjE;;;;;OAKG;IACH,gBAAgB,CAAC,EAAE,gBAAgB,CAAC,IAAI,CAAC,CAAC;CAC3C;AAED;;;;GAIG;AACH,MAAM,WAAW,wBAAyB,SAAQ,uBAAuB;CAAG;AAE5E;;;;;;GAMG;AACH,qBAAa,iBAAiB;IAC5B,OAAO,CAAC,OAAO,CAAS;IACxB,OAAO,CAAC,UAAU,CAAC,CAAmB;IACtC,OAAO,CAAC,SAAS,CAAmC;IACpD,OAAO,CAAC,OAAO,CAA2B;IAC1C,OAAO,CAAC,MAAM,CAAS;IACvB,OAAO,CAAC,OAAO,CAAS;IAExB,OAAO,CAAC,SAAS,CAAC,CAAS;IAC3B,OAAO,CAAC,gBAAgB,CAAC,CAAyB;IAElD;;;;OAIG;gBACS,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,wBAAwB;IA6B/D;;;;;OAKG;IACH,IAAI,GAAG,WAEN;IA0BD;;;;;;;OAOG;IACH,OAAO,CACL,UAAU,EAAE,gBAAgB,EAC5B,QAAQ,CAAC,EAAE,CAAC,eAAe,EAAE,OAAO,KAAK,IAAI;IAiB/C;;OAEG;IACH,SAAS;IAkBT;;;;;;;;;;;;OAYG;IACH,UAAU,CAAC,KAAK,SAAS,iBAAiB,CAAC,OAAO,CAAC,EACjD,KAAK,EAAE,KAAK,EACZ,GAAG,cAAc,EAAE,cAAc,CAAC,KAAK,EAAE,iBAAiB,CAAC,GAC1D,KAAK,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;IAyDnC;;;;;;;;;OASG;IACH,QAAQ,CAAC,QAAQ,SAAS,iBAAiB,CAAC,UAAU,CAAC,EACrD,QAAQ,EAAE,QAAQ,EAClB,GAAG,cAAc,EAAE,cAAc,CAC/B,QAAQ,EACR,eAAe,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CACxC,GACA,OAAO,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;IAMxC;;;;;;;;OAQG;IACH,MAAM,CAAC,MAAM,SAAS,iBAAiB,CAAC,QAAQ,CAAC,EAC/C,MAAM,EAAE,MAAM,EACd,GAAG,IAAI,EAAE,gBAAgB,CAAC,MAAM,CAAC,GAChC,OAAO,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;IAKtC;;;;;;;;;;;OAWG;IACH,KAAK,CAAC,KAAK,SAAS,iBAAiB,CAAC,OAAO,CAAC,EAC5C,KAAK,EAAE,KAAK,EACZ,GAAG,IAAI,EAAE,gBAAgB,CAAC,KAAK,CAAC,GAC/B,OAAO,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;IAkBrC;;;;;OAKG;IACH,eAAe,IAAI,eAAe;IAIlC;;;;OAIG;IACH,IAAI,MAAM,IAAI,MAAM,CAEnB;IAED;;;;;;;OAOG;IACG,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;IAW5B,OAAO,CAAC,UAAU;CAUnB;AAMD;;;;;;;;GAQG;AACH,wBAAgB,SAAS,IAAI,iBAAiB,CAE7C;AAED;;;;;;;;;GASG;AACH,eAAO,MAAM,cAAc,EAAE,KAAK,CAAC,EAAE,CAAC;IACpC,MAAM,EAAE,iBAAiB,CAAC;IAC1B,QAAQ,CAAC,EAAE,KAAK,CAAC,SAAS,CAAC;CAC5B,CAMA,CAAC;AAEF,MAAM,MAAM,sBAAsB,CAAC,OAAO,SAAS,iBAAiB,CAAC,GAAG,CAAC,IACvE,OAAO,CAAC,OAAO,CAAC,SAAS,WAAW,GAChC,CAAC,IAAI,CAAC,EAAE,WAAW,GAAG,MAAM,CAAC,GAC7B,CAAC,IAAI,EAAE,OAAO,CAAC,OAAO,CAAC,GAAG,MAAM,CAAC,CAAC;AAExC;;;;;;;;;;;;;;;GAeG;AACH,wBAAgB,QAAQ,CAAC,KAAK,SAAS,iBAAiB,CAAC,OAAO,CAAC,EAC/D,KAAK,EAAE,KAAK,EACZ,GAAG,IAAI,EAAE,sBAAsB,CAAC,KAAK,CAAC,GACrC,KAAK,CAAC,aAAa,CAAC,GAAG,SAAS,CA4BlC;AAED;;;;;;;;;;;;;;;;;;GAkBG;AACH,wBAAgB,WAAW,CAAC,QAAQ,SAAS,iBAAiB,CAAC,UAAU,CAAC,EACxE,QAAQ,EAAE,QAAQ,GACjB,aAAa,CAAC,QAAQ,CAAC,CAmBzB;AAED;;;;;;;;;;;;;;;;;GAiBG;AACH,wBAAgB,SAAS,CAAC,MAAM,SAAS,iBAAiB,CAAC,QAAQ,CAAC,EAClE,MAAM,EAAE,MAAM,GACb,WAAW,CAAC,MAAM,CAAC,CAmBrB"}
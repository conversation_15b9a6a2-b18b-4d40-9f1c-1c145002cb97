@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
    --color-background: var(--background);
    --color-foreground: var(--foreground);
    --font-sans: var(--font-apple-system);
    --font-mono: var(--font-sf-mono);
    --color-sidebar-ring: var(--sidebar-ring);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar: var(--sidebar);
    --color-chart-5: var(--chart-5);
    --color-chart-4: var(--chart-4);
    --color-chart-3: var(--chart-3);
    --color-chart-2: var(--chart-2);
    --color-chart-1: var(--chart-1);
    --color-ring: var(--ring);
    --color-input: var(--input);
    --color-border: var(--border);
    --color-destructive: var(--destructive);
    --color-accent-foreground: var(--accent-foreground);
    --color-accent: var(--accent);
    --color-muted-foreground: var(--muted-foreground);
    --color-muted: var(--muted);
    --color-secondary-foreground: var(--secondary-foreground);
    --color-secondary: var(--secondary);
    --color-primary-foreground: var(--primary-foreground);
    --color-primary: var(--primary);
    --color-popover-foreground: var(--popover-foreground);
    --color-popover: var(--popover);
    --color-card-foreground: var(--card-foreground);
    --color-card: var(--card);
    --radius-sm: 8px;
    --radius-md: 10px;
    --radius-lg: 12px;
    --radius-xl: 20px;
}

:root {
    /* Apple Light Theme Colors */
    --background: #ffffff;
    --foreground: #000000;
    --card: #ffffff;
    --card-foreground: #000000;
    --popover: #ffffff;
    --popover-foreground: #000000;
    --primary: #0071e3; /* Apple Blue */
    --primary-foreground: #ffffff;
    --secondary: #f5f5f7; /* Apple Light Gray */
    --secondary-foreground: #1d1d1f;
    --muted: #f5f5f7;
    --muted-foreground: #86868b;
    --accent: #0071e3; /* Apple Blue */
    --accent-foreground: #ffffff;
    --destructive: #ff3b30; /* Apple Red */
    --destructive-foreground: #ffffff;
    --border: #e2e2e7; /* Lighter, more subtle border */
    --input: #f5f5f7;
    --ring: #0071e3;
    --chart-1: #0071e3; /* Apple Blue */
    --chart-2: #34c759; /* Apple Green */
    --chart-3: #ff9500; /* Apple Orange */
    --chart-4: #ffcc00; /* Apple Yellow */
    --chart-5: #af52de; /* Apple Purple */
    --sidebar: #f5f5f7;
    --sidebar-foreground: #1d1d1f;
    --sidebar-primary: #0071e3;
    --sidebar-primary-foreground: #ffffff;
    --sidebar-accent: #e8e8ed;
    --sidebar-accent-foreground: #1d1d1f;
    --sidebar-border: #e2e2e7; /* Lighter border */
    --sidebar-ring: #0071e3;
    /* Apple Fonts */
    --font-apple-system:
        -apple-system, BlinkMacSystemFont, "San Francisco", "Helvetica Neue",
        Helvetica, sans-serif;
    --font-sf-mono: "SF Mono", Menlo, monospace;
    --font-sans: var(--font-apple-system);
    --font-serif: "New York", Georgia, serif;
    --font-mono: var(--font-sf-mono);
    /* Apple uses more rounded corners */
    --radius: 10px;
    /* Apple-style shadows */
    --shadow-2xs: 0px 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-xs: 0px 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-sm: 0px 2px 4px rgba(0, 0, 0, 0.1);
    --shadow: 0px 2px 6px rgba(0, 0, 0, 0.1);
    --shadow-md: 0px 4px 8px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0px 8px 16px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0px 12px 24px rgba(0, 0, 0, 0.1);
    --shadow-2xl: 0px 16px 32px rgba(0, 0, 0, 0.1);
}

@layer base {
    * {
        @apply border-border outline-ring/50;
    }
    body {
        @apply bg-background text-foreground font-sans;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
    button,
    input,
    select,
    textarea {
        @apply focus:outline-none focus:ring-2 focus:ring-primary/50 transition-all duration-200;
    }
    button {
        @apply hover:cursor-pointer;
    }
    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
        @apply font-medium tracking-tight;
    }
}

.dark {
    --sidebar: hsl(240 5.9% 10%);
    --sidebar-foreground: hsl(240 4.8% 95.9%);
    --sidebar-primary: hsl(224.3 76.3% 48%);
    --sidebar-primary-foreground: hsl(0 0% 100%);
    --sidebar-accent: hsl(240 3.7% 15.9%);
    --sidebar-accent-foreground: hsl(240 4.8% 95.9%);
    --sidebar-border: hsl(240 3.7% 15.9%);
    --sidebar-ring: hsl(217.2 91.2% 59.8%);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
    }
  body {
    @apply bg-background text-foreground;
    }
}

"use client";
import { <PERSON> } from "react-router";
import { Search, FileText, Calendar, TrendingUp, AlertTriangle, Plus } from "lucide-react";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { Progress } from "~/components/ui/progress";

export default function DashboardPage() {
  // Mock data for demonstration
  const stats = {
    totalGrants: 25,
    activeApplications: 5,
    upcomingDeadlines: 3,
    successRate: 68
  };

  const recentApplications = [
    {
      id: 1,
      title: "NSF Research Grant",
      organization: "National Science Foundation",
      amount: "$500,000",
      deadline: "2024-03-15",
      status: "In Progress",
      progress: 65
    },
    {
      id: 2,
      title: "Community Development Grant",
      organization: "HUD",
      amount: "$250,000",
      deadline: "2024-02-28",
      status: "Draft",
      progress: 25
    }
  ];

  const upcomingDeadlines = [
    { title: "EPA Grant Application", deadline: "2024-02-10", daysLeft: 11 },
    { title: "DOE Progress Report", deadline: "2024-02-15", daysLeft: 16 },
    { title: "NSF Final Report", deadline: "2024-02-20", daysLeft: 21 }
  ];

  return (
    <div className="flex flex-1 flex-col">
      <div className="@container/main flex flex-1 flex-col gap-2">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          {/* Header */}
          <div className="px-4 lg:px-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold">Dashboard</h1>
                <p className="text-muted-foreground">
                  Welcome back! Here's your grant management overview.
                </p>
              </div>
              <Button asChild>
                <Link to="/dashboard/grants">
                  <Plus className="mr-2 h-4 w-4" />
                  Find Grants
                </Link>
              </Button>
            </div>
          </div>

          {/* Key Metrics */}
          <div className="px-4 lg:px-6">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Available Grants</CardTitle>
                  <Search className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.totalGrants}</div>
                  <p className="text-xs text-muted-foreground">
                    +3 new this week
                  </p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Active Applications</CardTitle>
                  <FileText className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.activeApplications}</div>
                  <p className="text-xs text-muted-foreground">
                    2 due this month
                  </p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Upcoming Deadlines</CardTitle>
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.upcomingDeadlines}</div>
                  <p className="text-xs text-muted-foreground">
                    Next in 11 days
                  </p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
                  <TrendingUp className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.successRate}%</div>
                  <p className="text-xs text-muted-foreground">
                    +12% from last year
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="px-4 lg:px-6">
            <div className="grid gap-4 md:grid-cols-3">
              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <Link to="/dashboard/grants" className="block">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Search className="h-5 w-5" />
                      Discover Grants
                    </CardTitle>
                    <CardDescription>
                      Find new funding opportunities that match your needs
                    </CardDescription>
                  </CardHeader>
                </Link>
              </Card>
              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <Link to="/dashboard/applications" className="block">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <FileText className="h-5 w-5" />
                      Manage Applications
                    </CardTitle>
                    <CardDescription>
                      Track and update your grant applications
                    </CardDescription>
                  </CardHeader>
                </Link>
              </Card>
              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <Link to="/dashboard/deadlines" className="block">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Calendar className="h-5 w-5" />
                      View Deadlines
                    </CardTitle>
                    <CardDescription>
                      Stay on top of important dates and deadlines
                    </CardDescription>
                  </CardHeader>
                </Link>
              </Card>
            </div>
          </div>

          {/* Recent Applications and Upcoming Deadlines */}
          <div className="px-4 lg:px-6">
            <div className="grid gap-6 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Recent Applications</CardTitle>
                  <CardDescription>Your latest grant applications</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {recentApplications.map((app) => (
                    <div key={app.id} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium">{app.title}</p>
                          <p className="text-sm text-muted-foreground">{app.organization}</p>
                        </div>
                        <Badge variant="secondary">{app.status}</Badge>
                      </div>
                      <div className="space-y-1">
                        <div className="flex items-center justify-between text-sm">
                          <span>Progress</span>
                          <span>{app.progress}%</span>
                        </div>
                        <Progress value={app.progress} className="h-2" />
                      </div>
                    </div>
                  ))}
                  <Button variant="outline" className="w-full" asChild>
                    <Link to="/dashboard/applications">View All Applications</Link>
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Upcoming Deadlines</CardTitle>
                  <CardDescription>Important dates to remember</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {upcomingDeadlines.map((deadline, index) => (
                    <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-muted/50">
                      <div className="flex items-center gap-3">
                        <AlertTriangle className="h-4 w-4 text-orange-500" />
                        <div>
                          <p className="font-medium">{deadline.title}</p>
                          <p className="text-sm text-muted-foreground">{deadline.deadline}</p>
                        </div>
                      </div>
                      <Badge variant={deadline.daysLeft <= 14 ? "destructive" : "secondary"}>
                        {deadline.daysLeft} days
                      </Badge>
                    </div>
                  ))}
                  <Button variant="outline" className="w-full" asChild>
                    <Link to="/dashboard/deadlines">View All Deadlines</Link>
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

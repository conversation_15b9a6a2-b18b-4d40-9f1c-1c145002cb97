"use client";
import { TrendingUp, DollarSign, FileText, Calendar, Target } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Progress } from "~/components/ui/progress";

export default function AnalyticsPage() {
  // Mock data for demonstration
  const stats = {
    totalApplications: 12,
    activeApplications: 5,
    submittedApplications: 7,
    approvedApplications: 3,
    totalFundingRequested: 2500000,
    totalFundingReceived: 750000,
    successRate: 25,
    averageApplicationTime: 45
  };

  const recentActivity = [
    { action: "Application submitted", grant: "NSF Research Grant", date: "2024-01-15" },
    { action: "Document uploaded", grant: "EPA Environmental Grant", date: "2024-01-14" },
    { action: "Application started", grant: "Community Development Grant", date: "2024-01-12" },
    { action: "Grant approved", grant: "DOE Research Grant", date: "2024-01-10" }
  ];

  const grantsByCategory = [
    { category: "Research", count: 5, percentage: 42 },
    { category: "Community", count: 3, percentage: 25 },
    { category: "Environment", count: 2, percentage: 17 },
    { category: "Education", count: 2, percentage: 16 }
  ];

  return (
    <div className="flex flex-1 flex-col">
      <div className="@container/main flex flex-1 flex-col gap-2">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          {/* Header */}
          <div className="px-4 lg:px-6">
            <div>
              <h1 className="text-3xl font-bold">Analytics & Reports</h1>
              <p className="text-muted-foreground">
                Track your grant management performance and insights
              </p>
            </div>
          </div>

          {/* Key Metrics */}
          <div className="px-4 lg:px-6">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Applications</CardTitle>
                  <FileText className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.totalApplications}</div>
                  <p className="text-xs text-muted-foreground">
                    +2 from last month
                  </p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
                  <Target className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.successRate}%</div>
                  <p className="text-xs text-muted-foreground">
                    +5% from last quarter
                  </p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Funding Requested</CardTitle>
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    ${(stats.totalFundingRequested / 1000000).toFixed(1)}M
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Total across all applications
                  </p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Funding Received</CardTitle>
                  <TrendingUp className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    ${(stats.totalFundingReceived / 1000).toFixed(0)}K
                  </div>
                  <p className="text-xs text-muted-foreground">
                    From approved grants
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Application Status Overview */}
          <div className="px-4 lg:px-6">
            <div className="grid gap-6 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Application Status</CardTitle>
                  <CardDescription>Current status of all applications</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Active Applications</span>
                      <span className="text-sm font-medium">{stats.activeApplications}</span>
                    </div>
                    <Progress value={(stats.activeApplications / stats.totalApplications) * 100} />
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Submitted Applications</span>
                      <span className="text-sm font-medium">{stats.submittedApplications}</span>
                    </div>
                    <Progress value={(stats.submittedApplications / stats.totalApplications) * 100} />
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Approved Applications</span>
                      <span className="text-sm font-medium">{stats.approvedApplications}</span>
                    </div>
                    <Progress value={(stats.approvedApplications / stats.totalApplications) * 100} />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Grants by Category</CardTitle>
                  <CardDescription>Distribution of applications by category</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {grantsByCategory.map((category, index) => (
                    <div key={index} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm">{category.category}</span>
                        <span className="text-sm font-medium">{category.count} grants</span>
                      </div>
                      <Progress value={category.percentage} />
                    </div>
                  ))}
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Recent Activity */}
          <div className="px-4 lg:px-6">
            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
                <CardDescription>Latest updates and actions</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentActivity.map((activity, index) => (
                    <div key={index} className="flex items-center gap-4 p-3 rounded-lg bg-muted/50">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <div className="flex-1">
                        <p className="text-sm font-medium">{activity.action}</p>
                        <p className="text-sm text-muted-foreground">{activity.grant}</p>
                      </div>
                      <span className="text-sm text-muted-foreground">{activity.date}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}

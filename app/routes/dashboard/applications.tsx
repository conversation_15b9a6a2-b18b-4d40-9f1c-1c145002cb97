"use client";
import { Plus, Calendar, FileText, Clock } from "lucide-react";
import { But<PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { Progress } from "~/components/ui/progress";

export default function ApplicationsPage() {
  // Mock data for demonstration
  const applications = [
    {
      id: 1,
      grantTitle: "National Science Foundation Research Grant",
      organization: "NSF",
      amount: "$500,000",
      deadline: "2024-03-15",
      status: "In Progress",
      progress: 65,
      lastUpdated: "2024-01-15",
      priority: "High"
    },
    {
      id: 2,
      grantTitle: "Community Development Block Grant",
      organization: "HUD",
      amount: "$250,000",
      deadline: "2024-02-28",
      status: "Draft",
      progress: 25,
      lastUpdated: "2024-01-10",
      priority: "Medium"
    },
    {
      id: 3,
      grantTitle: "Environmental Protection Agency Grant",
      organization: "EPA",
      amount: "$150,000",
      deadline: "2024-04-10",
      status: "Submitted",
      progress: 100,
      lastUpdated: "2024-01-05",
      priority: "Low"
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Draft": return "secondary";
      case "In Progress": return "default";
      case "Submitted": return "success";
      case "Under Review": return "warning";
      case "Approved": return "success";
      case "Rejected": return "destructive";
      default: return "secondary";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "High": return "destructive";
      case "Medium": return "warning";
      case "Low": return "secondary";
      default: return "secondary";
    }
  };

  return (
    <div className="flex flex-1 flex-col">
      <div className="@container/main flex flex-1 flex-col gap-2">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          {/* Header */}
          <div className="px-4 lg:px-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold">Grant Applications</h1>
                <p className="text-muted-foreground">
                  Track and manage your grant applications
                </p>
              </div>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                New Application
              </Button>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="px-4 lg:px-6">
            <div className="grid gap-4 md:grid-cols-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Applications</CardTitle>
                  <FileText className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">3</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">In Progress</CardTitle>
                  <Clock className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">1</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Submitted</CardTitle>
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">1</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Value</CardTitle>
                  <FileText className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">$900K</div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Applications List */}
          <div className="px-4 lg:px-6">
            <div className="space-y-4">
              {applications.map((application) => (
                <Card key={application.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="text-lg">
                          {application.grantTitle}
                        </CardTitle>
                        <CardDescription className="mt-1">
                          {application.organization} • {application.amount}
                        </CardDescription>
                      </div>
                      <div className="flex gap-2">
                        <Badge variant={getPriorityColor(application.priority)}>
                          {application.priority}
                        </Badge>
                        <Badge variant={getStatusColor(application.status)}>
                          {application.status}
                        </Badge>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium">Progress</span>
                          <span className="text-sm text-muted-foreground">
                            {application.progress}%
                          </span>
                        </div>
                        <Progress value={application.progress} className="h-2" />
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <div>
                          <span className="text-muted-foreground">Deadline: </span>
                          <span className="font-medium">{application.deadline}</span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Last updated: </span>
                          <span className="font-medium">{application.lastUpdated}</span>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button size="sm" variant="default">
                          Continue Application
                        </Button>
                        <Button size="sm" variant="outline">
                          View Details
                        </Button>
                        <Button size="sm" variant="outline">
                          Documents
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

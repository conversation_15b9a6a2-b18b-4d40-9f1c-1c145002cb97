"use client";
import { Calendar, Clock, AlertTriangle, CheckCircle } from "lucide-react";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";

export default function DeadlinesPage() {
  // Mock data for demonstration
  const deadlines = [
    {
      id: 1,
      title: "NSF Research Grant Application",
      organization: "National Science Foundation",
      deadline: "2024-03-15",
      daysLeft: 45,
      status: "In Progress",
      priority: "High",
      type: "Application Deadline"
    },
    {
      id: 2,
      title: "Community Development Block Grant",
      organization: "HUD",
      deadline: "2024-02-28",
      daysLeft: 29,
      status: "Draft",
      priority: "Medium",
      type: "Application Deadline"
    },
    {
      id: 3,
      title: "EPA Grant - Progress Report",
      organization: "Environmental Protection Agency",
      deadline: "2024-02-10",
      daysLeft: 11,
      status: "Pending",
      priority: "High",
      type: "Progress Report"
    },
    {
      id: 4,
      title: "DOE Research Grant - Final Report",
      organization: "Department of Energy",
      deadline: "2024-01-25",
      daysLeft: -5,
      status: "Overdue",
      priority: "Critical",
      type: "Final Report"
    }
  ];

  const getUrgencyColor = (daysLeft: number) => {
    if (daysLeft < 0) return "destructive";
    if (daysLeft <= 7) return "destructive";
    if (daysLeft <= 30) return "warning";
    return "secondary";
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Completed": return <CheckCircle className="h-4 w-4" />;
      case "Overdue": return <AlertTriangle className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const formatDaysLeft = (daysLeft: number) => {
    if (daysLeft < 0) return `${Math.abs(daysLeft)} days overdue`;
    if (daysLeft === 0) return "Due today";
    if (daysLeft === 1) return "1 day left";
    return `${daysLeft} days left`;
  };

  return (
    <div className="flex flex-1 flex-col">
      <div className="@container/main flex flex-1 flex-col gap-2">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          {/* Header */}
          <div className="px-4 lg:px-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold">Deadlines & Calendar</h1>
                <p className="text-muted-foreground">
                  Track important dates and deadlines
                </p>
              </div>
              <Button>
                <Calendar className="mr-2 h-4 w-4" />
                Calendar View
              </Button>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="px-4 lg:px-6">
            <div className="grid gap-4 md:grid-cols-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Due This Week</CardTitle>
                  <AlertTriangle className="h-4 w-4 text-destructive" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-destructive">2</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Due This Month</CardTitle>
                  <Clock className="h-4 w-4 text-warning" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-warning">3</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Overdue</CardTitle>
                  <AlertTriangle className="h-4 w-4 text-destructive" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-destructive">1</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Completed</CardTitle>
                  <CheckCircle className="h-4 w-4 text-green-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-green-600">5</div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Deadlines List */}
          <div className="px-4 lg:px-6">
            <div className="space-y-4">
              {deadlines.map((deadline) => (
                <Card key={deadline.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="text-lg flex items-center gap-2">
                          {getStatusIcon(deadline.status)}
                          {deadline.title}
                        </CardTitle>
                        <CardDescription className="mt-1">
                          {deadline.organization} • {deadline.type}
                        </CardDescription>
                      </div>
                      <div className="flex gap-2">
                        <Badge variant={getUrgencyColor(deadline.daysLeft)}>
                          {formatDaysLeft(deadline.daysLeft)}
                        </Badge>
                        <Badge variant="outline">
                          {deadline.priority}
                        </Badge>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div>
                          <p className="text-sm font-medium">Deadline</p>
                          <p className="text-lg font-semibold">{deadline.deadline}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium">Status</p>
                          <p className="text-sm text-muted-foreground">{deadline.status}</p>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button size="sm" variant="default">
                          View Details
                        </Button>
                        <Button size="sm" variant="outline">
                          Set Reminder
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

"use client";
import { Upload, FileText, Download, Share, Folder, Search } from "lucide-react";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";

export default function DocumentsPage() {
  // Mock data for demonstration
  const documents = [
    {
      id: 1,
      name: "NSF Research Proposal Draft v3.docx",
      type: "Proposal",
      size: "2.4 MB",
      lastModified: "2024-01-15",
      status: "Draft",
      grantTitle: "NSF Research Grant",
      version: "v3"
    },
    {
      id: 2,
      name: "Budget Spreadsheet - Community Grant.xlsx",
      type: "Budget",
      size: "856 KB",
      lastModified: "2024-01-12",
      status: "Final",
      grantTitle: "Community Development Grant",
      version: "v1"
    },
    {
      id: 3,
      name: "EPA Grant Application Form.pdf",
      type: "Application",
      size: "1.2 MB",
      lastModified: "2024-01-10",
      status: "Submitted",
      grantTitle: "EPA Environmental Grant",
      version: "Final"
    },
    {
      id: 4,
      name: "Letters of Support - Combined.pdf",
      type: "Supporting Documents",
      size: "3.1 MB",
      lastModified: "2024-01-08",
      status: "Final",
      grantTitle: "NSF Research Grant",
      version: "v1"
    }
  ];

  const folders = [
    { name: "NSF Research Grant", count: 12, lastModified: "2024-01-15" },
    { name: "Community Development Grant", count: 8, lastModified: "2024-01-12" },
    { name: "EPA Environmental Grant", count: 6, lastModified: "2024-01-10" },
    { name: "Templates", count: 15, lastModified: "2024-01-05" }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Draft": return "secondary";
      case "Review": return "warning";
      case "Final": return "success";
      case "Submitted": return "default";
      default: return "secondary";
    }
  };

  const getFileIcon = (type: string) => {
    return <FileText className="h-4 w-4" />;
  };

  return (
    <div className="flex flex-1 flex-col">
      <div className="@container/main flex flex-1 flex-col gap-2">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          {/* Header */}
          <div className="px-4 lg:px-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold">Document Library</h1>
                <p className="text-muted-foreground">
                  Manage and organize your grant documents
                </p>
              </div>
              <Button>
                <Upload className="mr-2 h-4 w-4" />
                Upload Document
              </Button>
            </div>
          </div>

          {/* Search and Actions */}
          <div className="px-4 lg:px-6">
            <div className="flex gap-4 mb-6">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search documents..."
                    className="pl-10"
                  />
                </div>
              </div>
              <Button variant="outline">
                <Folder className="mr-2 h-4 w-4" />
                New Folder
              </Button>
            </div>
          </div>

          {/* Folders Section */}
          <div className="px-4 lg:px-6">
            <h2 className="text-xl font-semibold mb-4">Folders</h2>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-8">
              {folders.map((folder, index) => (
                <Card key={index} className="hover:shadow-lg transition-shadow cursor-pointer">
                  <CardHeader className="pb-3">
                    <div className="flex items-center gap-3">
                      <Folder className="h-8 w-8 text-blue-500" />
                      <div className="flex-1">
                        <CardTitle className="text-sm">{folder.name}</CardTitle>
                        <CardDescription className="text-xs">
                          {folder.count} files
                        </CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <p className="text-xs text-muted-foreground">
                      Modified {folder.lastModified}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Recent Documents */}
          <div className="px-4 lg:px-6">
            <h2 className="text-xl font-semibold mb-4">Recent Documents</h2>
            <div className="space-y-3">
              {documents.map((document) => (
                <Card key={document.id} className="hover:shadow-lg transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3 flex-1">
                        {getFileIcon(document.type)}
                        <div className="flex-1">
                          <h3 className="font-medium">{document.name}</h3>
                          <div className="flex items-center gap-4 text-sm text-muted-foreground">
                            <span>{document.grantTitle}</span>
                            <span>•</span>
                            <span>{document.size}</span>
                            <span>•</span>
                            <span>Modified {document.lastModified}</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <Badge variant={getStatusColor(document.status)}>
                          {document.status}
                        </Badge>
                        <Badge variant="outline">
                          {document.version}
                        </Badge>
                        <div className="flex gap-1">
                          <Button size="sm" variant="ghost">
                            <Download className="h-4 w-4" />
                          </Button>
                          <Button size="sm" variant="ghost">
                            <Share className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

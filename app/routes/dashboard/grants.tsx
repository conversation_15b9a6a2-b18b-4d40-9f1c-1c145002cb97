"use client";
import { Search, Filter, Plus } from "lucide-react";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";

export default function GrantsPage() {
  // Mock data for demonstration
  const grants = [
    {
      id: 1,
      title: "National Science Foundation Research Grant",
      organization: "NSF",
      amount: "$500,000",
      deadline: "2024-03-15",
      category: "Research",
      status: "Open",
      description: "Funding for innovative research in computer science and engineering fields."
    },
    {
      id: 2,
      title: "Community Development Block Grant",
      organization: "HUD",
      amount: "$250,000",
      deadline: "2024-02-28",
      category: "Community",
      status: "Open",
      description: "Support for community development and housing initiatives."
    },
    {
      id: 3,
      title: "Environmental Protection Agency Grant",
      organization: "EPA",
      amount: "$150,000",
      deadline: "2024-04-10",
      category: "Environment",
      status: "Open",
      description: "Funding for environmental protection and sustainability projects."
    }
  ];

  return (
    <div className="flex flex-1 flex-col">
      <div className="@container/main flex flex-1 flex-col gap-2">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          {/* Header */}
          <div className="px-4 lg:px-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold">Grant Discovery</h1>
                <p className="text-muted-foreground">
                  Discover and explore funding opportunities
                </p>
              </div>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Add Custom Grant
              </Button>
            </div>
          </div>

          {/* Search and Filters */}
          <div className="px-4 lg:px-6">
            <div className="flex gap-4 mb-6">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search grants by title, organization, or keywords..."
                    className="pl-10"
                  />
                </div>
              </div>
              <Button variant="outline">
                <Filter className="mr-2 h-4 w-4" />
                Filters
              </Button>
            </div>
          </div>

          {/* Grants Grid */}
          <div className="px-4 lg:px-6">
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {grants.map((grant) => (
                <Card key={grant.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="text-lg line-clamp-2">
                          {grant.title}
                        </CardTitle>
                        <CardDescription className="mt-1">
                          {grant.organization}
                        </CardDescription>
                      </div>
                      <Badge variant="secondary">{grant.status}</Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <p className="text-sm text-muted-foreground line-clamp-3">
                        {grant.description}
                      </p>
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium">Amount</p>
                          <p className="text-lg font-bold text-green-600">
                            {grant.amount}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-medium">Deadline</p>
                          <p className="text-sm text-muted-foreground">
                            {grant.deadline}
                          </p>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Badge variant="outline">{grant.category}</Badge>
                      </div>
                      <div className="flex gap-2 pt-2">
                        <Button size="sm" className="flex-1">
                          View Details
                        </Button>
                        <Button size="sm" variant="outline" className="flex-1">
                          Save Grant
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

{"name": "grantguru-webapp", "private": true, "type": "module", "version": "1.0.0", "description": "GrantGuru - A comprehensive grant management platform for discovering, tracking, and managing grant applications", "author": "GrantGuru Team", "keywords": ["grants", "funding", "management", "nonprofit", "research", "applications"], "scripts": {"build": "react-router build", "dev": "react-router dev", "start": "react-router-serve ./build/server/index.js", "typecheck": "react-router typegen && tsc"}, "dependencies": {"@ai-sdk/openai": "^1.3.22", "@ai-sdk/react": "^1.2.12", "@clerk/react-router": "^1.4.8", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@react-router/node": "^7.5.3", "@react-router/serve": "^7.5.3", "@tabler/icons-react": "^3.33.0", "@tanstack/react-table": "^8.21.3", "@vercel/analytics": "^1.5.0", "@vercel/react-router": "^1.1.2", "ai": "^4.3.16", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "convex": "^1.24.3", "isbot": "^5.1.27", "lucide-react": "^0.511.0", "motion": "^12.15.0", "next-themes": "^0.4.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-markdown": "^10.1.0", "react-router": "^7.5.3", "recharts": "^2.15.3", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0", "vaul": "^1.1.2", "zod": "^3.25.39"}, "devDependencies": {"@react-router/dev": "^7.5.3", "@tailwindcss/vite": "^4.1.4", "@types/node": "^20", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "tailwindcss": "^4.1.4", "tw-animate-css": "^1.3.2", "typescript": "^5.8.3", "vite": "^6.3.3", "vite-tsconfig-paths": "^5.1.4"}}